#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الثيمات والألوان المتجاوب
يدير الألوان والثيمات المختلفة للتطبيق
"""

from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QPalette, QColor
from PyQt5.QtWidgets import QApplication

class ThemeManager(QObject):
    """مدير الثيمات"""
    
    theme_changed = pyqtSignal(str)  # اسم الثيم الجديد
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes = self.load_themes()
        
    def load_themes(self):
        """تحميل الثيمات المتاحة"""
        return {
            "light": {
                "name": "الثيم الفاتح",
                "colors": {
                    "primary": "#3498db",
                    "secondary": "#2c3e50", 
                    "success": "#27ae60",
                    "danger": "#e74c3c",
                    "warning": "#f39c12",
                    "info": "#9b59b6",
                    "background": "#ffffff",
                    "surface": "#f8f9fa",
                    "text_primary": "#2c3e50",
                    "text_secondary": "#7f8c8d",
                    "border": "#ecf0f1",
                    "shadow": "rgba(0,0,0,0.1)"
                }
            },
            "dark": {
                "name": "الثيم المظلم",
                "colors": {
                    "primary": "#3498db",
                    "secondary": "#34495e",
                    "success": "#27ae60", 
                    "danger": "#e74c3c",
                    "warning": "#f39c12",
                    "info": "#9b59b6",
                    "background": "#2c3e50",
                    "surface": "#34495e",
                    "text_primary": "#ecf0f1",
                    "text_secondary": "#bdc3c7",
                    "border": "#34495e",
                    "shadow": "rgba(0,0,0,0.3)"
                }
            },
            "blue": {
                "name": "الثيم الأزرق",
                "colors": {
                    "primary": "#2980b9",
                    "secondary": "#34495e",
                    "success": "#27ae60",
                    "danger": "#e74c3c", 
                    "warning": "#f39c12",
                    "info": "#8e44ad",
                    "background": "#ffffff",
                    "surface": "#f4f6f7",
                    "text_primary": "#2c3e50",
                    "text_secondary": "#7f8c8d",
                    "border": "#d5dbdb",
                    "shadow": "rgba(41,128,185,0.1)"
                }
            },
            "green": {
                "name": "الثيم الأخضر",
                "colors": {
                    "primary": "#27ae60",
                    "secondary": "#2c3e50",
                    "success": "#229954",
                    "danger": "#e74c3c",
                    "warning": "#f39c12", 
                    "info": "#9b59b6",
                    "background": "#ffffff",
                    "surface": "#f0f9f4",
                    "text_primary": "#2c3e50",
                    "text_secondary": "#7f8c8d",
                    "border": "#d5f4e6",
                    "shadow": "rgba(39,174,96,0.1)"
                }
            }
        }
    
    def get_available_themes(self):
        """الحصول على قائمة الثيمات المتاحة"""
        return [(key, theme["name"]) for key, theme in self.themes.items()]
    
    def set_theme(self, theme_name):
        """تعيين الثيم"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.apply_theme(theme_name)
            self.theme_changed.emit(theme_name)
            
    def apply_theme(self, theme_name):
        """تطبيق الثيم"""
        if theme_name not in self.themes:
            return
            
        theme = self.themes[theme_name]
        colors = theme["colors"]
        
        # إنشاء CSS للثيم
        css = self.generate_theme_css(colors)
        
        # تطبيق CSS على التطبيق
        app = QApplication.instance()
        if app:
            app.setStyleSheet(css)
            
    def generate_theme_css(self, colors):
        """إنشاء CSS للثيم"""
        css = f"""
        /* ثيم {self.current_theme} */
        
        /* المتغيرات العامة */
        * {{
            qproperty-primaryColor: {colors['primary']};
            qproperty-secondaryColor: {colors['secondary']};
            qproperty-successColor: {colors['success']};
            qproperty-dangerColor: {colors['danger']};
            qproperty-warningColor: {colors['warning']};
            qproperty-infoColor: {colors['info']};
            qproperty-backgroundColor: {colors['background']};
            qproperty-surfaceColor: {colors['surface']};
            qproperty-textPrimaryColor: {colors['text_primary']};
            qproperty-textSecondaryColor: {colors['text_secondary']};
            qproperty-borderColor: {colors['border']};
        }}
        
        /* النافذة الرئيسية */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* الشريط الجانبي */
        #sidebar {{
            background-color: {colors['secondary']};
            border-right: 1px solid {colors['border']};
        }}
        
        #sidebar QPushButton {{
            background-color: transparent;
            color: {colors['text_primary'] if self.current_theme == 'dark' else '#ecf0f1'};
            border: none;
            padding: 15px 20px;
            text-align: right;
        }}
        
        #sidebar QPushButton:hover {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        #sidebar QPushButton:checked {{
            background-color: {colors['primary']};
            color: white;
            border-left: 4px solid {colors['danger']};
        }}
        
        /* المحتوى الرئيسي */
        #main_content {{
            background-color: {colors['background']};
            border: 1px solid {colors['border']};
            border-radius: 12px;
        }}
        
        /* البطاقات */
        QFrame#card, QFrame.card {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 12px;
            color: {colors['text_primary']};
        }}
        
        QFrame#card:hover, QFrame.card:hover {{
            border-color: {colors['primary']};
            box-shadow: 0 4px 16px {colors['shadow']};
        }}
        
        /* الأزرار */
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
        }}
        
        QPushButton:hover {{
            background-color: {self.darken_color(colors['primary'])};
        }}
        
        QPushButton.btn-success {{
            background-color: {colors['success']};
        }}
        
        QPushButton.btn-success:hover {{
            background-color: {self.darken_color(colors['success'])};
        }}
        
        QPushButton.btn-danger {{
            background-color: {colors['danger']};
        }}
        
        QPushButton.btn-danger:hover {{
            background-color: {self.darken_color(colors['danger'])};
        }}
        
        QPushButton.btn-warning {{
            background-color: {colors['warning']};
        }}
        
        QPushButton.btn-warning:hover {{
            background-color: {self.darken_color(colors['warning'])};
        }}
        
        /* حقول الإدخال */
        QLineEdit, QTextEdit {{
            background-color: {colors['background']};
            border: 2px solid {colors['border']};
            border-radius: 8px;
            color: {colors['text_primary']};
            padding: 12px 16px;
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {colors['primary']};
        }}
        
        /* القوائم المنسدلة */
        QComboBox {{
            background-color: {colors['background']};
            border: 2px solid {colors['border']};
            border-radius: 8px;
            color: {colors['text_primary']};
            padding: 12px 16px;
        }}
        
        QComboBox:focus {{
            border-color: {colors['primary']};
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors['surface']};
            border: 2px solid {colors['primary']};
            selection-background-color: {colors['primary']};
            selection-color: white;
        }}
        
        /* الجداول */
        QTableWidget {{
            background-color: {colors['background']};
            alternate-background-color: {colors['surface']};
            border: 2px solid {colors['border']};
            gridline-color: {colors['border']};
            color: {colors['text_primary']};
        }}
        
        QHeaderView::section {{
            background-color: {colors['secondary']};
            color: {colors['text_primary'] if self.current_theme == 'dark' else 'white'};
            border: none;
            padding: 12px;
        }}
        
        /* التسميات */
        QLabel {{
            color: {colors['text_primary']};
        }}
        
        QLabel#title, QLabel.page-title {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}
        
        QLabel#subtitle, QLabel.page-subtitle {{
            color: {colors['text_secondary']};
        }}
        
        /* شريط التمرير */
        QScrollBar:vertical {{
            background-color: {colors['surface']};
            border: none;
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['border']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['text_secondary']};
        }}
        """
        
        return css
    
    def darken_color(self, color_hex):
        """تغميق لون"""
        try:
            # إزالة # إذا وجدت
            color_hex = color_hex.lstrip('#')
            
            # تحويل إلى RGB
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16) 
            b = int(color_hex[4:6], 16)
            
            # تغميق بنسبة 20%
            r = max(0, int(r * 0.8))
            g = max(0, int(g * 0.8))
            b = max(0, int(b * 0.8))
            
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except:
            return color_hex
    
    def lighten_color(self, color_hex):
        """تفتيح لون"""
        try:
            color_hex = color_hex.lstrip('#')
            
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16)
            b = int(color_hex[4:6], 16)
            
            # تفتيح بنسبة 20%
            r = min(255, int(r * 1.2))
            g = min(255, int(g * 1.2))
            b = min(255, int(b * 1.2))
            
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except:
            return color_hex
    
    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.current_theme
    
    def get_theme_colors(self, theme_name=None):
        """الحصول على ألوان ثيم معين"""
        if theme_name is None:
            theme_name = self.current_theme
            
        if theme_name in self.themes:
            return self.themes[theme_name]["colors"]
        return {}
    
    def create_custom_theme(self, name, colors):
        """إنشاء ثيم مخصص"""
        self.themes[name] = {
            "name": name,
            "colors": colors
        }
        
    def export_theme(self, theme_name):
        """تصدير ثيم"""
        if theme_name in self.themes:
            return self.themes[theme_name]
        return None
    
    def import_theme(self, theme_data):
        """استيراد ثيم"""
        try:
            if "name" in theme_data and "colors" in theme_data:
                theme_name = theme_data["name"].lower().replace(" ", "_")
                self.themes[theme_name] = theme_data
                return True
        except:
            pass
        return False

class ResponsiveThemeManager(ThemeManager):
    """مدير الثيمات المتجاوب"""
    
    def __init__(self):
        super().__init__()
        self.size_specific_themes = {}
        
    def set_size_specific_theme(self, size_category, theme_name):
        """تعيين ثيم خاص بحجم معين"""
        self.size_specific_themes[size_category] = theme_name
        
    def apply_responsive_theme(self, size_category):
        """تطبيق ثيم متجاوب حسب الحجم"""
        # البحث عن ثيم خاص بالحجم
        if size_category in self.size_specific_themes:
            theme_name = self.size_specific_themes[size_category]
            self.set_theme(theme_name)
        else:
            # تطبيق تحسينات عامة حسب الحجم
            self.apply_size_optimizations(size_category)
            
    def apply_size_optimizations(self, size_category):
        """تطبيق تحسينات حسب الحجم"""
        current_colors = self.get_theme_colors()
        
        # تحسينات خاصة بالهاتف
        if size_category in ['xs', 'sm']:
            # ألوان أكثر تباينًا للهاتف
            optimized_colors = current_colors.copy()
            optimized_colors['border'] = self.darken_color(current_colors['border'])
            
            # تطبيق CSS محسن للهاتف
            mobile_css = self.generate_mobile_css(optimized_colors)
            app = QApplication.instance()
            if app:
                current_css = app.styleSheet()
                app.setStyleSheet(current_css + mobile_css)
                
    def generate_mobile_css(self, colors):
        """إنشاء CSS خاص بالهاتف"""
        return f"""
        /* تحسينات الهاتف */
        QPushButton {{
            min-height: 44px;
            font-size: 16px;
        }}
        
        QLineEdit, QComboBox {{
            min-height: 44px;
            font-size: 16px;
        }}
        
        QTableWidget {{
            font-size: 14px;
        }}
        
        QTableWidget::item {{
            padding: 8px 6px;
        }}
        """
