import re
import json
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import statistics

class AIAssistant:
    """مساعد ذكي لتحليل المصاريف وتقديم النصائح"""
    
    def __init__(self, database):
        self.db = database
        self.category_keywords = self.load_category_keywords()
        self.spending_patterns = {}
        
    def load_category_keywords(self) -> Dict[str, List[str]]:
        """تحميل الكلمات المفتاحية لكل تصنيف"""
        return {
            'طعام وشراب': [
                'مطعم', 'كافيه', 'قهوة', 'عشاء', 'غداء', 'فطار', 'بيتزا', 'برجر', 
                'شاي', 'عصير', 'ماء', 'سوبر ماركت', 'بقالة', 'خضار', 'فواكه',
                'لحم', 'دجاج', 'سمك', 'خبز', 'حليب', 'جبن', 'زبدة', 'أرز',
                'مكدونالدز', 'كنتاكي', 'بيتزا هت', 'دومينوز', 'ستار بكس', 'كوستا'
            ],
            'مواصلات': [
                'بنزين', 'وقود', 'تاكسي', 'أوبر', 'كريم', 'باص', 'مترو', 'قطار',
                'طيران', 'تذكرة', 'سفر', 'مطار', 'سيارة', 'صيانة', 'زيت', 'إطارات',
                'رخصة', 'تأمين', 'مخالفة', 'موقف', 'غسيل سيارة'
            ],
            'ترفيه': [
                'سينما', 'مسرح', 'حفلة', 'لعبة', 'بلايستيشن', 'نتفليكس', 'يوتيوب',
                'موسيقى', 'كتاب', 'مجلة', 'رياضة', 'نادي', 'سباحة', 'جيم',
                'حديقة', 'ملاهي', 'بولينج', 'كرة', 'تنس', 'كرة قدم'
            ],
            'صحة': [
                'طبيب', 'مستشفى', 'عيادة', 'دواء', 'صيدلية', 'تحليل', 'أشعة',
                'عملية', 'علاج', 'فيتامين', 'مكمل', 'نظارة', 'أسنان', 'جراحة',
                'تأمين صحي', 'فحص', 'كشف'
            ],
            'تسوق': [
                'ملابس', 'حذاء', 'شنطة', 'ساعة', 'عطر', 'مكياج', 'شامبو', 'صابون',
                'منظف', 'مول', 'متجر', 'أونلاين', 'أمازون', 'نون', 'سوق', 'تسوق',
                'قميص', 'بنطلون', 'فستان', 'جاكيت', 'معطف'
            ],
            'فواتير': [
                'كهرباء', 'ماء', 'غاز', 'هاتف', 'انترنت', 'كابل', 'تلفزيون',
                'اشتراك', 'فاتورة', 'رسوم', 'ضريبة', 'بنك', 'قرض', 'تقسيط',
                'إيجار', 'سكن', 'منزل', 'شقة'
            ],
            'تعليم': [
                'مدرسة', 'جامعة', 'كلية', 'دورة', 'كورس', 'كتاب', 'قلم', 'دفتر',
                'حقيبة', 'رسوم', 'مصاريف', 'تعليم', 'درس', 'معلم', 'أستاذ',
                'امتحان', 'شهادة', 'تدريب'
            ]
        }
    
    def auto_categorize_expense(self, expense_name: str, amount: float) -> str:
        """تصنيف تلقائي للمصروف بناءً على الاسم والمبلغ"""
        expense_name_lower = expense_name.lower()
        
        # البحث عن الكلمات المفتاحية
        category_scores = {}
        
        for category, keywords in self.category_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in expense_name_lower:
                    # إعطاء نقاط أعلى للكلمات الأطول (أكثر تحديداً)
                    score += len(keyword)
            
            if score > 0:
                category_scores[category] = score
        
        # إذا وُجدت مطابقات، اختر الأعلى نقاطاً
        if category_scores:
            best_category = max(category_scores, key=category_scores.get)
            return best_category
        
        # تصنيف بناءً على المبلغ إذا لم توجد كلمات مفتاحية
        if amount <= 50:
            return 'طعام وشراب'  # المبالغ الصغيرة غالباً طعام
        elif amount <= 200:
            return 'تسوق'  # المبالغ المتوسطة غالباً تسوق
        elif amount <= 1000:
            return 'فواتير'  # المبالغ الكبيرة غالباً فواتير
        else:
            return 'أخرى'  # المبالغ الكبيرة جداً
    
    def analyze_spending_patterns(self, days: int = 30) -> Dict:
        """تحليل أنماط الإنفاق"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        expenses = self.db.get_expenses(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        if not expenses:
            return {'patterns': [], 'insights': []}
        
        patterns = {
            'daily_average': 0,
            'peak_spending_day': '',
            'most_expensive_category': '',
            'spending_trend': 'stable',
            'weekend_vs_weekday': {},
            'time_patterns': {}
        }
        
        # حساب المتوسط اليومي
        total_amount = sum(exp['amount'] for exp in expenses)
        patterns['daily_average'] = total_amount / days
        
        # تحليل الإنفاق حسب اليوم
        daily_spending = {}
        for expense in expenses:
            date = expense['date']
            if date not in daily_spending:
                daily_spending[date] = 0
            daily_spending[date] += expense['amount']
        
        if daily_spending:
            peak_day = max(daily_spending, key=daily_spending.get)
            patterns['peak_spending_day'] = peak_day
        
        # تحليل الإنفاق حسب التصنيف
        category_spending = {}
        for expense in expenses:
            category = expense['category']
            if category not in category_spending:
                category_spending[category] = 0
            category_spending[category] += expense['amount']
        
        if category_spending:
            patterns['most_expensive_category'] = max(category_spending, key=category_spending.get)
        
        # تحليل اتجاه الإنفاق
        if len(daily_spending) >= 7:
            recent_week = list(daily_spending.values())[-7:]
            previous_week = list(daily_spending.values())[-14:-7] if len(daily_spending) >= 14 else []
            
            if previous_week:
                recent_avg = statistics.mean(recent_week)
                previous_avg = statistics.mean(previous_week)
                
                if recent_avg > previous_avg * 1.1:
                    patterns['spending_trend'] = 'increasing'
                elif recent_avg < previous_avg * 0.9:
                    patterns['spending_trend'] = 'decreasing'
        
        return patterns
    
    def generate_personalized_tips(self) -> List[str]:
        """توليد نصائح شخصية بناءً على أنماط الإنفاق"""
        patterns = self.analyze_spending_patterns()
        tips = []
        
        # نصائح بناءً على المتوسط اليومي
        daily_avg = patterns.get('daily_average', 0)
        if daily_avg > 200:
            tips.append("💡 متوسط إنفاقك اليومي مرتفع. حاول تحديد ميزانية يومية والالتزام بها.")
        elif daily_avg < 50:
            tips.append("👍 إنفاقك اليومي معتدل. استمر على هذا النهج!")
        
        # نصائح بناءً على التصنيف الأكثر إنفاقاً
        top_category = patterns.get('most_expensive_category', '')
        if top_category == 'طعام وشراب':
            tips.append("🍽️ تنفق كثيراً على الطعام. جرب الطبخ في المنزل أكثر لتوفير المال.")
        elif top_category == 'ترفيه':
            tips.append("🎮 إنفاقك على الترفيه مرتفع. حدد ميزانية شهرية للترفيه.")
        elif top_category == 'تسوق':
            tips.append("🛍️ تتسوق كثيراً. اكتب قائمة بما تحتاجه قبل التسوق.")
        
        # نصائح بناءً على الاتجاه
        trend = patterns.get('spending_trend', 'stable')
        if trend == 'increasing':
            tips.append("📈 إنفاقك في ازدياد. راجع مصاريفك وحدد ما يمكن تقليله.")
        elif trend == 'decreasing':
            tips.append("📉 أحسنت! إنفاقك في تحسن. استمر على هذا النهج.")
        
        # نصائح عامة
        current_month = datetime.now().month
        current_year = datetime.now().year
        
        # فحص الميزانيات
        budget_data = self.db.get_budget_vs_spending(current_year, current_month)
        for budget in budget_data:
            if budget['status'] == 'over':
                tips.append(f"⚠️ تجاوزت ميزانية {budget['category']}. حاول تقليل الإنفاق في هذا التصنيف.")
            elif budget['status'] == 'warning':
                tips.append(f"🟡 اقتربت من حد ميزانية {budget['category']}. انتبه لإنفاقك.")
        
        # نصائح للأهداف
        goals = self.db.get_financial_goals()
        for goal in goals:
            if goal['progress_percentage'] < 25:
                tips.append(f"🎯 تقدمك نحو هدف '{goal['name']}' بطيء. حاول زيادة المبلغ المدخر.")
            elif goal['progress_percentage'] >= 75:
                tips.append(f"🎉 أنت قريب من تحقيق هدف '{goal['name']}'! استمر!")
        
        # إضافة نصائح عامة إذا لم توجد نصائح شخصية
        if not tips:
            general_tips = [
                "💰 احرص على تسجيل جميع مصاريفك يومياً للحصول على صورة واضحة عن إنفاقك.",
                "📊 راجع تقاريرك الشهرية لفهم أنماط إنفاقك بشكل أفضل.",
                "🎯 حدد أهدافاً مالية واضحة واعمل على تحقيقها تدريجياً.",
                "💡 استخدم قاعدة 50/30/20: 50% للضروريات، 30% للرغبات، 20% للادخار.",
                "🔍 ابحث عن طرق لتوفير المال في مصاريفك الثابتة مثل الفواتير."
            ]
            tips.extend(general_tips[:3])  # أضف 3 نصائح عامة
        
        return tips[:5]  # أرجع أقصى 5 نصائح
    
    def predict_monthly_spending(self) -> Dict:
        """توقع الإنفاق الشهري بناءً على البيانات الحالية"""
        current_date = datetime.now()
        month_start = current_date.replace(day=1)
        
        # جلب مصاريف الشهر الحالي
        current_month_expenses = self.db.get_expenses(
            month_start.strftime('%Y-%m-%d'),
            current_date.strftime('%Y-%m-%d')
        )
        
        if not current_month_expenses:
            return {'prediction': 0, 'confidence': 'low', 'message': 'لا توجد بيانات كافية للتوقع'}
        
        # حساب المتوسط اليومي للشهر الحالي
        days_passed = current_date.day
        total_spent = sum(exp['amount'] for exp in current_month_expenses)
        daily_average = total_spent / days_passed
        
        # عدد الأيام المتبقية في الشهر
        if current_date.month == 12:
            next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            next_month = current_date.replace(month=current_date.month + 1, day=1)
        
        days_in_month = (next_month - month_start).days
        days_remaining = days_in_month - days_passed
        
        # التوقع البسيط
        predicted_remaining = daily_average * days_remaining
        predicted_total = total_spent + predicted_remaining
        
        # تحديد مستوى الثقة
        if days_passed >= 15:
            confidence = 'high'
        elif days_passed >= 7:
            confidence = 'medium'
        else:
            confidence = 'low'
        
        # رسالة توضيحية
        if days_passed < 7:
            message = 'التوقع أولي - يحتاج المزيد من البيانات'
        elif predicted_total > total_spent * 1.5:
            message = 'توقع إنفاق مرتفع - راجع ميزانيتك'
        else:
            message = 'التوقع ضمن المعدل الطبيعي'
        
        return {
            'current_spending': total_spent,
            'predicted_total': predicted_total,
            'predicted_remaining': predicted_remaining,
            'daily_average': daily_average,
            'days_passed': days_passed,
            'days_remaining': days_remaining,
            'confidence': confidence,
            'message': message
        }
    
    def detect_unusual_spending(self, threshold: float = 2.0) -> List[Dict]:
        """كشف الإنفاق غير العادي"""
        # جلب آخر 30 يوم
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        expenses = self.db.get_expenses(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        if len(expenses) < 10:  # نحتاج بيانات كافية
            return []
        
        # حساب المتوسط والانحراف المعياري
        amounts = [exp['amount'] for exp in expenses]
        mean_amount = statistics.mean(amounts)
        
        if len(amounts) > 1:
            stdev_amount = statistics.stdev(amounts)
        else:
            return []
        
        # البحث عن المصاريف غير العادية
        unusual_expenses = []
        for expense in expenses:
            if expense['amount'] > mean_amount + (threshold * stdev_amount):
                unusual_expenses.append({
                    'expense': expense,
                    'deviation': expense['amount'] - mean_amount,
                    'severity': 'high' if expense['amount'] > mean_amount + (3 * stdev_amount) else 'medium'
                })
        
        return unusual_expenses
    
    def get_category_insights(self, category: str, days: int = 30) -> Dict:
        """تحليل مفصل لتصنيف معين"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # جلب مصاريف التصنيف
        all_expenses = self.db.get_expenses(
            start_date.strftime('%Y-%m-%d'),
            end_date.strftime('%Y-%m-%d')
        )
        
        category_expenses = [exp for exp in all_expenses if exp['category'] == category]
        
        if not category_expenses:
            return {'message': f'لا توجد مصاريف في تصنيف {category}'}
        
        total_amount = sum(exp['amount'] for exp in category_expenses)
        avg_amount = total_amount / len(category_expenses)
        max_expense = max(category_expenses, key=lambda x: x['amount'])
        min_expense = min(category_expenses, key=lambda x: x['amount'])
        
        # تحليل التكرار
        frequency = len(category_expenses) / days
        
        # نصائح خاصة بالتصنيف
        tips = []
        if category == 'طعام وشراب' and frequency > 2:
            tips.append("تتسوق للطعام كثيراً. جرب التسوق مرة واحدة أسبوعياً.")
        elif category == 'ترفيه' and avg_amount > 100:
            tips.append("متوسط إنفاقك على الترفيه مرتفع. ابحث عن أنشطة أقل تكلفة.")
        
        return {
            'total_amount': total_amount,
            'average_amount': avg_amount,
            'frequency': frequency,
            'max_expense': max_expense,
            'min_expense': min_expense,
            'tips': tips
        }
