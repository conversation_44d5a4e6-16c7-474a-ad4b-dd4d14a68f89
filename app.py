#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة المصاريف الشخصية
تطبيق سطح المكتب باستخدام PyQt5 مع دعم كامل للعربية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025
الإصدار: 1.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
from main_window import MainWindow

def create_splash_screen():
    """إنشاء شاشة البداية"""
    # إنشاء صورة للشاشة الترحيبية
    pixmap = QPixmap(400, 300)
    pixmap.fill(QColor(52, 73, 94))  # لون خلفية داكن
    
    painter = <PERSON><PERSON><PERSON><PERSON>(pixmap)
    painter.setPen(QColor(255, 255, 255))
    painter.setFont(QFont("Segoe UI", 24, QFont.Bold))
    
    # رسم النص
    painter.drawText(pixmap.rect(), Qt.AlignCenter, "💰\nإدارة المصاريف الشخصية\n\nجاري التحميل...")
    painter.end()
    
    return QSplashScreen(pixmap)

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        'PyQt5',
        'matplotlib',
        'reportlab',
        'arabic_reshaper',
        'bidi'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = "المكتبات التالية غير مثبتة:\n\n"
        error_msg += "\n".join(f"- {module}" for module in missing_modules)
        error_msg += "\n\nيرجى تثبيتها باستخدام:\npip install " + " ".join(missing_modules)
        
        app = QApplication(sys.argv)
        QMessageBox.critical(None, "مكتبات مفقودة", error_msg)
        sys.exit(1)

def setup_application():
    """إعداد التطبيق"""
    app = QApplication(sys.argv)
    
    # تعيين معلومات التطبيق
    app.setApplicationName("إدارة المصاريف الشخصية")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Personal Finance Manager")
    
    # تعيين الخط الافتراضي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # تعيين اتجاه التطبيق من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين نمط التطبيق
    app.setStyle('Fusion')
    
    return app

def main():
    """الدالة الرئيسية"""
    try:
        # فحص المكتبات المطلوبة
        check_dependencies()
        
        # إعداد التطبيق
        app = setup_application()
        
        # إنشاء شاشة البداية
        splash = create_splash_screen()
        splash.show()
        
        # معالجة الأحداث لعرض الشاشة الترحيبية
        app.processEvents()
        
        # تأخير لعرض الشاشة الترحيبية
        QTimer.singleShot(2000, splash.close)
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        
        # إغلاق الشاشة الترحيبية وعرض النافذة الرئيسية
        def show_main_window():
            splash.finish(window)
            window.show()
        
        QTimer.singleShot(2000, show_main_window)
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        # معالجة الأخطاء العامة
        app = QApplication(sys.argv) if 'app' not in locals() else app
        error_msg = f"حدث خطأ غير متوقع:\n\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المكتبات المطلوبة."
        QMessageBox.critical(None, "خطأ", error_msg)
        sys.exit(1)

if __name__ == "__main__":
    main()
