#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لبرنامج إدارة المصاريف الشخصية
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from main_window import MainWindow

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعدادات التطبيق
    app.setApplicationName("إدارة المصاريف الشخصية")
    app.setApplicationVersion("1.0")
    
    # تعيين الخط والاتجاه
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء وعرض النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
