import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QFrame, QGridLayout, QComboBox, QPushButton,
                             QScrollArea, QSizePolicy, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QTouchDevice

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib import font_manager
import arabic_reshaper
from bidi.algorithm import get_display

class DashboardPage(QWidget):
    """صفحة لوحة المعلومات الرئيسية"""
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.init_ui()
        self.setup_matplotlib()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # تحديد نوع الجهاز
        self.is_touch_device = self.detect_touch_device()

        # إنشاء منطقة التمرير إذا كان جهاز حاسوب
        if not self.is_touch_device:
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            # إنشاء الويدجت الداخلي
            scroll_widget = QWidget()
            scroll_area.setWidget(scroll_widget)

            # التخطيط الداخلي
            layout = QVBoxLayout(scroll_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(20)

            main_layout.addWidget(scroll_area)
        else:
            # للأجهزة اللمسية - بدون تمرير
            layout = QVBoxLayout()
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            main_layout.addLayout(layout)

        # العنوان الرئيسي
        title = QLabel("📊 لوحة المعلومات")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignRight)
        layout.addWidget(title)

        # فلتر الفترة الزمنية
        self.create_period_filter(layout)

        # بطاقات الإحصائيات
        self.create_stats_cards(layout)

        # الرسوم البيانية
        self.create_charts_section(layout)

        # إضافة مساحة مرنة في النهاية للأجهزة اللمسية
        if self.is_touch_device:
            layout.addStretch()

        # تحديث البيانات
        self.refresh_data()

    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            if hasattr(self, 'stats_layout'):
                # إعادة ترتيب بطاقات الإحصائيات
                if size_category == 'xs':
                    # عمود واحد للهواتف الصغيرة
                    self.rearrange_stats_grid(1)
                elif size_category == 'sm':
                    # عمودين للهواتف الكبيرة
                    self.rearrange_stats_grid(2)
                elif size_category == 'md':
                    # ثلاثة أعمدة للتابلت
                    self.rearrange_stats_grid(3)
                else:
                    # ثلاثة أعمدة لسطح المكتب
                    self.rearrange_stats_grid(3)

            # تحديث الرسوم البيانية
            self.update_charts_responsive_layout(size_category)

        except Exception as e:
            print(f"خطأ في تحديث التخطيط المتجاوب: {e}")

    def rearrange_stats_grid(self, columns):
        """إعادة ترتيب شبكة الإحصائيات"""
        try:
            if hasattr(self, 'stats_layout'):
                # قائمة البطاقات
                cards = [
                    self.total_card, self.count_card, self.avg_card,
                    self.max_card, self.budget_card, self.goals_card
                ]

                # إعادة ترتيب البطاقات
                for i, card in enumerate(cards):
                    if card:
                        row = i // columns
                        col = i % columns
                        self.stats_layout.addWidget(card, row, col)

        except Exception as e:
            print(f"خطأ في إعادة ترتيب الشبكة: {e}")

    def update_charts_responsive_layout(self, size_category):
        """تحديث تخطيط الرسوم البيانية للتجاوب"""
        try:
            # تحديث حجم الرسوم البيانية حسب الشاشة
            if hasattr(self, 'pie_chart') and hasattr(self, 'line_chart'):
                if size_category in ['xs', 'sm']:
                    # ترتيب عمودي للهواتف
                    self.pie_chart.setMaximumHeight(200)
                    self.line_chart.setMaximumHeight(200)
                else:
                    # ترتيب أفقي للشاشات الكبيرة
                    self.pie_chart.setMaximumHeight(300)
                    self.line_chart.setMaximumHeight(300)

        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")

    def detect_touch_device(self):
        """كشف نوع الجهاز - لمسي أم حاسوب"""
        try:
            # فحص وجود أجهزة لمسية
            touch_devices = QTouchDevice.devices()
            if touch_devices:
                return True

            # فحص حجم الشاشة كمؤشر إضافي
            screen = QApplication.primaryScreen()
            if screen:
                size = screen.size()
                # إذا كان العرض أقل من 768 بكسل، غالباً جهاز محمول
                if size.width() < 768:
                    return True

            return False
        except:
            # في حالة الخطأ، افترض أنه حاسوب
            return False
        
    def create_period_filter(self, parent_layout):
        """إنشاء فلتر الفترة الزمنية"""
        filter_frame = QFrame()
        filter_frame.setObjectName("card")
        filter_layout = QHBoxLayout(filter_frame)
        
        # تسمية الفلتر
        filter_label = QLabel("عرض البيانات لـ:")
        filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # قائمة الفترات
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم",
            "الأسبوع الحالي", 
            "الشهر الحالي",
            "آخر 30 يوم",
            "آخر 3 أشهر",
            "السنة الحالية",
            "جميع الفترات"
        ])
        self.period_combo.setCurrentText("الشهر الحالي")
        self.period_combo.currentTextChanged.connect(self.refresh_data)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.period_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        
        parent_layout.addWidget(filter_frame)
        
    def create_stats_cards(self, parent_layout):
        """إنشاء بطاقات الإحصائيات"""
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(10)
        stats_layout.setContentsMargins(10, 10, 10, 10)

        # بطاقة إجمالي المصاريف
        self.total_card = self.create_stat_card("💰", "إجمالي المصاريف", "0 ريال", "#e74c3c")

        # بطاقة عدد المصاريف
        self.count_card = self.create_stat_card("📝", "عدد المصاريف", "0", "#3498db")

        # بطاقة متوسط المصروف
        self.avg_card = self.create_stat_card("📊", "متوسط المصروف", "0 ريال", "#27ae60")

        # بطاقة أكبر مصروف
        self.max_card = self.create_stat_card("⬆️", "أكبر مصروف", "0 ريال", "#f39c12")

        # بطاقة الميزانيات
        self.budget_card = self.create_stat_card("💰", "الميزانيات", "0 من 0", "#9b59b6")

        # بطاقة الأهداف
        self.goals_card = self.create_stat_card("🎯", "الأهداف المحققة", "0 من 0", "#1abc9c")

        # تعيين الحد الأدنى لحجم البطاقات
        all_cards = [self.total_card, self.count_card, self.avg_card, self.max_card, self.budget_card, self.goals_card]
        for card in all_cards:
            card.setMinimumSize(150, 80)  # تقليل الحد الأدنى للحجم
            card.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # حفظ مرجع للتخطيط لاستخدامه في التجاوب
        self.stats_layout = stats_layout

        # إضافة البطاقات للشبكة مع إعدادات متجاوبة (3 أعمدة)
        stats_layout.addWidget(self.total_card, 0, 0)
        stats_layout.addWidget(self.count_card, 0, 1)
        stats_layout.addWidget(self.avg_card, 0, 2)
        stats_layout.addWidget(self.max_card, 1, 0)
        stats_layout.addWidget(self.budget_card, 1, 1)
        stats_layout.addWidget(self.goals_card, 1, 2)

        # تعيين نسب الأعمدة (3 أعمدة)
        stats_layout.setColumnStretch(0, 1)
        stats_layout.setColumnStretch(1, 1)
        stats_layout.setColumnStretch(2, 1)

        parent_layout.addWidget(stats_frame)
        
    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("stat_card")
        card.setStyleSheet(f"""
            QFrame#stat_card {{
                background-color: {color};
                border: none;
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
                min-width: 180px;
                min-height: 100px;
            }}
            QFrame#stat_card QLabel {{
                color: white;
                font-weight: bold;
                background-color: transparent;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)

        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 20))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setFixedSize(30, 30)

        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        title_label.setWordWrap(True)  # السماح بتقسيم النص على أسطر متعددة
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label, 1)  # إعطاء مساحة أكبر للعنوان

        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setWordWrap(True)  # السماح بتقسيم النص
        value_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        layout.addLayout(header_layout)
        layout.addWidget(value_label)

        # حفظ مراجع للتحديث لاحقاً
        card.value_label = value_label
        card.title_label = title_label
        card.icon_label = icon_label

        return card
        
    def create_charts_section(self, parent_layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setObjectName("card")
        charts_layout = QVBoxLayout(charts_frame)

        # عنوان القسم
        charts_title = QLabel("📈 الرسوم البيانية")
        charts_title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        charts_title.setAlignment(Qt.AlignRight)
        charts_layout.addWidget(charts_title)

        # تخطيط الرسوم البيانية
        charts_grid = QHBoxLayout()

        # الرسم البياني الدائري للتصنيفات
        self.pie_chart = self.create_pie_chart()
        charts_grid.addWidget(self.pie_chart)

        # الرسم البياني الخطي للمصاريف اليومية
        self.line_chart = self.create_line_chart()
        charts_grid.addWidget(self.line_chart)

        charts_layout.addLayout(charts_grid)
        parent_layout.addWidget(charts_frame)
        
    def create_pie_chart(self):
        """إنشاء الرسم البياني الدائري"""
        figure = Figure(figsize=(6, 4), dpi=100)
        canvas = FigureCanvas(figure)
        canvas.setMinimumHeight(300)

        self.pie_ax = figure.add_subplot(111)
        self.pie_figure = figure

        return canvas

    def create_line_chart(self):
        """إنشاء الرسم البياني الخطي"""
        figure = Figure(figsize=(6, 4), dpi=100)
        canvas = FigureCanvas(figure)
        canvas.setMinimumHeight(300)

        self.line_ax = figure.add_subplot(111)
        self.line_figure = figure

        return canvas
        
    def setup_matplotlib(self):
        """إعداد matplotlib للعربية"""
        plt.rcParams['font.family'] = ['Tahoma', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
    def get_date_range(self):
        """الحصول على نطاق التاريخ حسب الفلتر المحدد"""
        period = self.period_combo.currentText()
        today = datetime.now().date()
        
        if period == "اليوم":
            return today.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "الأسبوع الحالي":
            start = today - timedelta(days=today.weekday())
            end = start + timedelta(days=6)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        elif period == "الشهر الحالي":
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        elif period == "آخر 30 يوم":
            start = today - timedelta(days=30)
            return start.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "آخر 3 أشهر":
            start = today - timedelta(days=90)
            return start.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "السنة الحالية":
            start = today.replace(month=1, day=1)
            end = today.replace(month=12, day=31)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        else:  # جميع الفترات
            return None, None

    def refresh_data(self):
        """تحديث جميع البيانات في الصفحة"""
        start_date, end_date = self.get_date_range()

        # تحديث الإحصائيات
        self.update_stats(start_date, end_date)

        # تحديث الرسوم البيانية
        self.update_pie_chart(start_date, end_date)
        self.update_line_chart(start_date, end_date)

        # تحديث التجاوب
        self.adjust_font_sizes()

    def update_stats(self, start_date, end_date):
        """تحديث بطاقات الإحصائيات"""
        # جلب البيانات
        expenses = self.db.get_expenses(start_date, end_date)
        total = self.db.get_total_expenses(start_date, end_date)

        # حساب الإحصائيات
        count = len(expenses)
        avg = total / count if count > 0 else 0
        max_expense = max([exp['amount'] for exp in expenses]) if expenses else 0

        # تنسيق النصوص بناءً على حجم النافذة
        window_width = self.width()
        window_height = self.height()

        # تحديد مستوى الاختصار بناءً على المساحة المتاحة
        if window_width < 600 or window_height < 400:
            # شاشة صغيرة جداً - نص مختصر جداً
            total_text = f"{total/1000:,.0f}K" if total >= 1000 else f"{total:,.0f}"
            avg_text = f"{avg/1000:,.0f}K" if avg >= 1000 else f"{avg:,.0f}"
            max_text = f"{max_expense/1000:,.0f}K" if max_expense >= 1000 else f"{max_expense:,.0f}"
        elif window_width < 800 or window_height < 500:
            # شاشة صغيرة - نص مختصر
            total_text = f"{total:,.0f}"
            avg_text = f"{avg:,.0f}"
            max_text = f"{max_expense:,.0f}"
        elif window_width < 1000 or window_height < 600:
            # شاشة متوسطة - نص متوسط
            total_text = f"{total:,.0f} ر.س"
            avg_text = f"{avg:,.0f} ر.س"
            max_text = f"{max_expense:,.0f} ر.س"
        else:
            # شاشة كبيرة - نص كامل
            total_text = f"{total:,.0f} ريال"
            avg_text = f"{avg:,.0f} ريال"
            max_text = f"{max_expense:,.0f} ريال"

        # تحديث البطاقات الأساسية
        self.total_card.value_label.setText(total_text)
        self.count_card.value_label.setText(str(count))
        self.avg_card.value_label.setText(avg_text)
        self.max_card.value_label.setText(max_text)

        # تحديث بيانات الميزانيات والأهداف
        self.update_budget_and_goals_stats()

    def update_budget_and_goals_stats(self):
        """تحديث إحصائيات الميزانيات والأهداف"""
        current_date = datetime.now()

        # إحصائيات الميزانيات
        budget_data = self.db.get_budget_vs_spending(current_date.year, current_date.month)
        total_budgets = len(budget_data)
        exceeded_budgets = len([b for b in budget_data if b['status'] == 'over'])

        if total_budgets > 0:
            budget_text = f"{exceeded_budgets} من {total_budgets}"
            if exceeded_budgets > 0:
                # تغيير لون البطاقة إذا كان هناك تجاوز
                self.budget_card.setStyleSheet(self.budget_card.styleSheet().replace("#9b59b6", "#e74c3c"))
            else:
                # إعادة اللون الطبيعي
                self.budget_card.setStyleSheet(self.budget_card.styleSheet().replace("#e74c3c", "#9b59b6"))
        else:
            budget_text = "لا توجد ميزانيات"

        self.budget_card.value_label.setText(budget_text)

        # إحصائيات الأهداف
        goals = self.db.get_financial_goals()
        total_goals = len(goals)
        completed_goals = len([g for g in goals if g['progress_percentage'] >= 100])

        if total_goals > 0:
            goals_text = f"{completed_goals} من {total_goals}"
        else:
            goals_text = "لا توجد أهداف"

        self.goals_card.value_label.setText(goals_text)

    def update_pie_chart(self, start_date, end_date):
        """تحديث الرسم البياني الدائري"""
        self.pie_ax.clear()

        # جلب البيانات حسب التصنيف
        category_data = self.db.get_expenses_by_category(start_date, end_date)

        if not category_data:
            self.pie_ax.text(0.5, 0.5, 'لا توجد بيانات',
                           horizontalalignment='center',
                           verticalalignment='center',
                           transform=self.pie_ax.transAxes,
                           fontsize=14)
        else:
            # تحضير البيانات
            categories = []
            amounts = []
            colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6',
                     '#1abc9c', '#34495e', '#95a5a6']

            for i, (category, amount) in enumerate(category_data.items()):
                # تحويل النص العربي للعرض الصحيح
                reshaped_text = arabic_reshaper.reshape(category)
                display_text = get_display(reshaped_text)
                categories.append(display_text)
                amounts.append(amount)

            # رسم المخطط الدائري
            wedges, texts, autotexts = self.pie_ax.pie(amounts, labels=categories,
                                                      colors=colors[:len(categories)],
                                                      autopct='%1.1f%%',
                                                      startangle=90)

            # تحسين النصوص
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        # تعيين العنوان
        title_text = arabic_reshaper.reshape("المصاريف حسب التصنيف")
        display_title = get_display(title_text)
        self.pie_ax.set_title(display_title, fontsize=14, fontweight='bold', pad=20)

        self.pie_figure.tight_layout()
        self.pie_chart.draw()

    def update_line_chart(self, start_date, end_date):
        """تحديث الرسم البياني الخطي"""
        self.line_ax.clear()

        # جلب البيانات
        expenses = self.db.get_expenses(start_date, end_date)

        if not expenses:
            self.line_ax.text(0.5, 0.5, 'لا توجد بيانات',
                            horizontalalignment='center',
                            verticalalignment='center',
                            transform=self.line_ax.transAxes,
                            fontsize=14)
        else:
            # تجميع البيانات حسب التاريخ
            daily_expenses = {}
            for expense in expenses:
                date = expense['date']
                if date in daily_expenses:
                    daily_expenses[date] += expense['amount']
                else:
                    daily_expenses[date] = expense['amount']

            # ترتيب البيانات حسب التاريخ
            sorted_dates = sorted(daily_expenses.keys())
            dates = [datetime.strptime(date, '%Y-%m-%d').date() for date in sorted_dates]
            amounts = [daily_expenses[date] for date in sorted_dates]

            # رسم المخطط الخطي
            self.line_ax.plot(dates, amounts, marker='o', linewidth=2,
                            markersize=6, color='#3498db')

            # تنسيق المحاور
            self.line_ax.set_xlabel('التاريخ', fontsize=12, fontweight='bold')
            self.line_ax.set_ylabel('المبلغ (ريال)', fontsize=12, fontweight='bold')

            # تنسيق تواريخ المحور السيني
            if len(dates) > 10:
                self.line_ax.xaxis.set_major_locator(mdates.WeekdayLocator())
                self.line_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            else:
                self.line_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))

            # تدوير تسميات التاريخ
            plt.setp(self.line_ax.xaxis.get_majorticklabels(), rotation=45)

            # إضافة شبكة
            self.line_ax.grid(True, alpha=0.3)

        # تعيين العنوان
        title_text = arabic_reshaper.reshape("المصاريف اليومية")
        display_title = get_display(title_text)
        self.line_ax.set_title(display_title, fontsize=14, fontweight='bold', pad=20)

        self.line_figure.tight_layout()
        self.line_chart.draw()

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة"""
        super().resizeEvent(event)
        self.adjust_font_sizes()

    def adjust_font_sizes(self):
        """تعديل أحجام الخطوط وتخطيط البطاقات بناءً على حجم النافذة"""
        try:
            # الحصول على أبعاد النافذة
            window_width = self.width()
            window_height = self.height()

            # تحديد أحجام الخطوط بناءً على أبعاد النافذة
            if window_width < 600 or window_height < 400:
                # شاشة صغيرة جداً - ترتيب عمودي
                icon_size = 12
                title_size = 7
                value_size = 9
                card_height = 50
                self.rearrange_cards_vertical()
            elif window_width < 800 or window_height < 500:
                # شاشة صغيرة
                icon_size = 14
                title_size = 8
                value_size = 10
                card_height = 60
                self.rearrange_cards_grid()
            elif window_width < 1000 or window_height < 600:
                # شاشة متوسطة صغيرة
                icon_size = 16
                title_size = 9
                value_size = 11
                card_height = 70
                self.rearrange_cards_grid()
            elif window_width < 1200 or window_height < 700:
                # شاشة متوسطة
                icon_size = 18
                title_size = 10
                value_size = 13
                card_height = 80
                self.rearrange_cards_grid()
            else:
                # شاشة كبيرة
                icon_size = 20
                title_size = 11
                value_size = 15
                card_height = 90
                self.rearrange_cards_grid()

            # تحديث خطوط البطاقات
            cards = [self.total_card, self.count_card, self.avg_card, self.max_card]
            for card in cards:
                if hasattr(card, 'icon_label'):
                    card.icon_label.setFont(QFont("Segoe UI", icon_size))
                if hasattr(card, 'title_label'):
                    card.title_label.setFont(QFont("Segoe UI", title_size, QFont.Bold))
                if hasattr(card, 'value_label'):
                    card.value_label.setFont(QFont("Segoe UI", value_size, QFont.Bold))

                # تحديث حجم البطاقة بناءً على المساحة المتاحة
                min_width = max(100, window_width // 6)  # عرض متكيف
                card.setMinimumSize(min_width, card_height)
                card.setMaximumHeight(card_height + 20)  # حد أقصى للارتفاع

        except:
            pass  # تجاهل الأخطاء في حالة عدم وجود البطاقات بعد

    def rearrange_cards_vertical(self):
        """إعادة ترتيب البطاقات عمودياً للشاشات الصغيرة جداً"""
        try:
            if hasattr(self, 'stats_layout'):
                # إزالة البطاقات من التخطيط الحالي
                cards = [self.total_card, self.count_card, self.avg_card, self.max_card]
                for card in cards:
                    self.stats_layout.removeWidget(card)

                # إعادة إضافتها في عمود واحد
                for i, card in enumerate(cards):
                    self.stats_layout.addWidget(card, i, 0, 1, 2)  # تمديد على عمودين
        except:
            pass

    def rearrange_cards_grid(self):
        """إعادة ترتيب البطاقات في شبكة 2x2"""
        try:
            if hasattr(self, 'stats_layout'):
                # إزالة البطاقات من التخطيط الحالي
                cards = [self.total_card, self.count_card, self.avg_card, self.max_card]
                for card in cards:
                    self.stats_layout.removeWidget(card)

                # إعادة إضافتها في شبكة 2x2
                self.stats_layout.addWidget(self.total_card, 0, 0)
                self.stats_layout.addWidget(self.count_card, 0, 1)
                self.stats_layout.addWidget(self.avg_card, 1, 0)
                self.stats_layout.addWidget(self.max_card, 1, 1)
        except:
            pass
