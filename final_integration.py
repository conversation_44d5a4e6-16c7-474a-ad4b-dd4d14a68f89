#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التكامل النهائي للنظام المتجاوب
يدمج جميع المكونات والتحسينات في التطبيق
"""

import os
import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

def integrate_responsive_system():
    """دمج النظام المتجاوب في الملف الرئيسي"""
    print("🔄 دمج النظام المتجاوب في الملف الرئيسي...")
    
    try:
        # قراءة الملف الرئيسي
        with open("main_window.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # إضافة استيرادات التحسينات اللمسية
        if "from touch_optimization import" not in content:
            touch_imports = """from touch_optimization import TouchOptimizer, MobileNavigationManager, TouchFeedbackManager, apply_touch_optimizations_to_app
from theme_manager import ThemeManager, ResponsiveThemeManager
from responsive_components import ResponsiveButton, ResponsiveCard, ResponsiveTable
"""
            content = content.replace(
                "from responsive_grid import ResponsiveContainer, ResponsiveGridWidget",
                f"from responsive_grid import ResponsiveContainer, ResponsiveGridWidget\n{touch_imports}"
            )
        
        # إضافة تهيئة النظام المتجاوب الكامل
        if "self.setup_complete_responsive_system()" not in content:
            setup_call = "\n        # إعداد النظام المتجاوب الكامل\n        self.setup_complete_responsive_system()"
            content = content.replace(
                "self.responsive_manager.size_changed.connect(self.layout_manager.update_layouts)",
                f"self.responsive_manager.size_changed.connect(self.layout_manager.update_layouts){setup_call}"
            )
        
        # إضافة دالة الإعداد الكامل
        if "def setup_complete_responsive_system(self):" not in content:
            complete_setup = '''
    def setup_complete_responsive_system(self):
        """إعداد النظام المتجاوب الكامل"""
        try:
            # إنشاء مدير الثيمات
            self.theme_manager = ResponsiveThemeManager()
            
            # تطبيق تحسينات اللمس
            self.touch_components = apply_touch_optimizations_to_app(self)
            
            # ربط إشارات الثيمات
            self.theme_manager.theme_changed.connect(self.on_theme_changed)
            
            # ربط تغيير الحجم مع الثيمات
            self.responsive_manager.size_changed.connect(self.theme_manager.apply_responsive_theme)
            
            # تطبيق الثيم الافتراضي
            self.theme_manager.set_theme("light")
            
            print("✅ تم إعداد النظام المتجاوب الكامل بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد النظام المتجاوب: {e}")
    
    def on_theme_changed(self, theme_name):
        """معالج تغيير الثيم"""
        print(f"تم تغيير الثيم إلى: {theme_name}")
        
        # تحديث جميع الصفحات
        self.update_all_pages_theme(theme_name)
    
    def update_all_pages_theme(self, theme_name):
        """تحديث ثيم جميع الصفحات"""
        try:
            pages = [
                self.dashboard_page, self.budget_page, self.goals_page,
                self.add_expense_page, self.manage_expenses_page,
                self.notifications_page, self.ai_insights_page
            ]
            
            for page in pages:
                if page and hasattr(page, 'update_theme'):
                    page.update_theme(theme_name)
                    
        except Exception as e:
            print(f"خطأ في تحديث ثيم الصفحات: {e}")
    
    def toggle_theme(self):
        """تبديل الثيم"""
        if hasattr(self, 'theme_manager'):
            current = self.theme_manager.get_current_theme()
            new_theme = "dark" if current == "light" else "light"
            self.theme_manager.set_theme(new_theme)
    
    def set_mobile_mode(self, enabled):
        """تفعيل/إلغاء وضع الهاتف"""
        try:
            if hasattr(self, 'touch_components') and self.touch_components:
                nav_manager = self.touch_components.get('nav_manager')
                if nav_manager:
                    if enabled:
                        nav_manager.setup_mobile_navigation()
                    else:
                        # إعادة الواجهة لوضع سطح المكتب
                        if hasattr(self, 'sidebar'):
                            self.sidebar.setVisible(True)
                            
        except Exception as e:
            print(f"خطأ في تعيين وضع الهاتف: {e}")
'''
            
            # إضافة الدوال قبل النهاية
            content = content.replace(
                "    def closeEvent(self, event):",
                f"{complete_setup}\n    def closeEvent(self, event):"
            )
        
        # حفظ الملف المحدث
        with open("main_window.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        print("✅ تم دمج النظام المتجاوب في الملف الرئيسي بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في دمج النظام المتجاوب: {e}")

def add_responsive_methods_to_pages():
    """إضافة دوال التجاوب لجميع الصفحات"""
    print("🔄 إضافة دوال التجاوب المتقدمة للصفحات...")
    
    pages_config = {
        "dashboard_page.py": {
            "class_name": "DashboardPage",
            "special_methods": """
    def update_theme(self, theme_name):
        \"\"\"تحديث ثيم الصفحة\"\"\"
        try:
            # تطبيق ألوان الثيم على الرسوم البيانية
            if hasattr(self, 'pie_chart') and hasattr(self, 'line_chart'):
                self.update_charts_theme(theme_name)
        except Exception as e:
            print(f"خطأ في تحديث ثيم لوحة المعلومات: {e}")
    
    def update_charts_theme(self, theme_name):
        \"\"\"تحديث ثيم الرسوم البيانية\"\"\"
        try:
            import matplotlib.pyplot as plt
            
            if theme_name == "dark":
                plt.style.use('dark_background')
            else:
                plt.style.use('default')
                
            # إعادة رسم الرسوم البيانية
            self.update_pie_chart()
            self.update_line_chart()
            
        except Exception as e:
            print(f"خطأ في تحديث ثيم الرسوم البيانية: {e}")
"""
        },
        "budget_page.py": {
            "class_name": "BudgetPage", 
            "special_methods": """
    def update_theme(self, theme_name):
        \"\"\"تحديث ثيم الصفحة\"\"\"
        try:
            # تحديث ألوان البطاقات
            self.update_budget_cards_theme(theme_name)
        except Exception as e:
            print(f"خطأ في تحديث ثيم الميزانيات: {e}")
    
    def update_budget_cards_theme(self, theme_name):
        \"\"\"تحديث ثيم بطاقات الميزانيات\"\"\"
        try:
            # إعادة تحميل البطاقات بالثيم الجديد
            self.load_budget_cards()
        except Exception as e:
            print(f"خطأ في تحديث ثيم البطاقات: {e}")
"""
        },
        "goals_page.py": {
            "class_name": "GoalsPage",
            "special_methods": """
    def update_theme(self, theme_name):
        \"\"\"تحديث ثيم الصفحة\"\"\"
        try:
            # تحديث ألوان أشرطة التقدم
            self.update_progress_bars_theme(theme_name)
        except Exception as e:
            print(f"خطأ في تحديث ثيم الأهداف: {e}")
    
    def update_progress_bars_theme(self, theme_name):
        \"\"\"تحديث ثيم أشرطة التقدم\"\"\"
        try:
            # إعادة تحميل الأهداف بالثيم الجديد
            self.load_goals()
        except Exception as e:
            print(f"خطأ في تحديث ثيم أشرطة التقدم: {e}")
"""
        }
    }
    
    for page_file, config in pages_config.items():
        if os.path.exists(page_file):
            try:
                with open(page_file, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # إضافة الدوال الخاصة إذا لم تكن موجودة
                if "def update_theme(self, theme_name):" not in content:
                    # البحث عن نهاية الكلاس
                    class_pattern = f"class {config['class_name']}"
                    if class_pattern in content:
                        # إضافة الدوال قبل النهاية
                        content = content.replace(
                            "    def update_responsive_layout(self, size_category):",
                            f"{config['special_methods']}\n    def update_responsive_layout(self, size_category):"
                        )
                
                # حفظ الملف
                with open(page_file, "w", encoding="utf-8") as f:
                    f.write(content)
                    
                print(f"✅ تم تحديث {page_file} بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في تحديث {page_file}: {e}")

def create_responsive_demo():
    """إنشاء عرض توضيحي للنظام المتجاوب"""
    print("🔄 إنشاء عرض توضيحي للنظام المتجاوب...")
    
    demo_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام المتجاوب المتقدم
يعرض جميع الميزات والتحسينات المطبقة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QWidget, QPushButton, QLabel, QComboBox, QSlider
from PyQt5.QtCore import Qt

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from responsive_manager import ResponsiveManager
from theme_manager import ResponsiveThemeManager
from responsive_components import ResponsiveButton, ResponsiveCard, ResponsiveTable
from touch_optimization import apply_touch_optimizations_to_app

class ResponsiveDemoWindow(QMainWindow):
    """نافذة العرض التوضيحي"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_responsive_system()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🎨 عرض توضيحي للنظام المتجاوب المتقدم")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(320, 480)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title = QLabel("🎨 النظام المتجاوب المتقدم")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title)
        
        # أدوات التحكم
        self.create_controls(layout)
        
        # البطاقات التوضيحية
        self.create_demo_cards(layout)
        
        # الجدول التوضيحي
        self.create_demo_table(layout)
        
    def create_controls(self, parent_layout):
        """إنشاء أدوات التحكم"""
        controls_layout = QHBoxLayout()
        
        # اختيار الثيم
        theme_label = QLabel("الثيم:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "مظلم", "أزرق", "أخضر"])
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        
        # زر تبديل الوضع
        self.mode_btn = ResponsiveButton("وضع الهاتف", "secondary")
        self.mode_btn.clicked.connect(self.toggle_mobile_mode)
        
        controls_layout.addWidget(theme_label)
        controls_layout.addWidget(self.theme_combo)
        controls_layout.addStretch()
        controls_layout.addWidget(self.mode_btn)
        
        parent_layout.addLayout(controls_layout)
        
    def create_demo_cards(self, parent_layout):
        """إنشاء البطاقات التوضيحية"""
        cards_layout = QHBoxLayout()
        
        # بطاقة الميزات
        features_card = ResponsiveCard("🚀 الميزات", "تصميم متجاوب متقدم")
        features_card.add_widget(QLabel("• تكيف تلقائي مع جميع الأحجام"))
        features_card.add_widget(QLabel("• ثيمات متعددة"))
        features_card.add_widget(QLabel("• تحسينات اللمس"))
        
        # بطاقة الأداء
        performance_card = ResponsiveCard("⚡ الأداء", "محسن للسرعة")
        performance_card.add_widget(QLabel("• تحميل سريع"))
        performance_card.add_widget(QLabel("• استهلاك ذاكرة منخفض"))
        performance_card.add_widget(QLabel("• رسوم متحركة سلسة"))
        
        # بطاقة التوافق
        compatibility_card = ResponsiveCard("📱 التوافق", "يعمل في كل مكان")
        compatibility_card.add_widget(QLabel("• سطح المكتب"))
        compatibility_card.add_widget(QLabel("• التابلت"))
        compatibility_card.add_widget(QLabel("• الهاتف"))
        
        cards_layout.addWidget(features_card)
        cards_layout.addWidget(performance_card)
        cards_layout.addWidget(compatibility_card)
        
        parent_layout.addLayout(cards_layout)
        
    def create_demo_table(self, parent_layout):
        """إنشاء الجدول التوضيحي"""
        table = ResponsiveTable(3, 4)
        table.setHorizontalHeaderLabels(["الميزة", "الحالة", "التقييم", "الملاحظات"])
        
        # ملء البيانات
        data = [
            ["التجاوب", "✅ مفعل", "ممتاز", "يعمل على جميع الأحجام"],
            ["الثيمات", "✅ مفعل", "ممتاز", "4 ثيمات متاحة"],
            ["اللمس", "✅ مفعل", "جيد جداً", "محسن للأجهزة اللمسية"]
        ]
        
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                table.setItem(row, col, table.create_item(cell_data))
        
        parent_layout.addWidget(table)
        
    def setup_responsive_system(self):
        """إعداد النظام المتجاوب"""
        # مدير التجاوب
        self.responsive_manager = ResponsiveManager(self)
        self.responsive_manager.size_changed.connect(self.on_size_changed)
        
        # مدير الثيمات
        self.theme_manager = ResponsiveThemeManager()
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
        # تحسينات اللمس
        self.touch_components = apply_touch_optimizations_to_app(self)
        
    def change_theme(self, theme_text):
        """تغيير الثيم"""
        theme_map = {
            "فاتح": "light",
            "مظلم": "dark", 
            "أزرق": "blue",
            "أخضر": "green"
        }
        
        theme_name = theme_map.get(theme_text, "light")
        self.theme_manager.set_theme(theme_name)
        
    def toggle_mobile_mode(self):
        """تبديل وضع الهاتف"""
        current_size = self.responsive_manager.current_size
        if current_size in ['xs', 'sm']:
            self.resize(1200, 800)  # وضع سطح المكتب
            self.mode_btn.setText("وضع الهاتف")
        else:
            self.resize(375, 667)   # وضع الهاتف
            self.mode_btn.setText("وضع سطح المكتب")
            
    def on_size_changed(self, size_category):
        """معالج تغيير الحجم"""
        size_names = {
            'xs': 'هاتف صغير',
            'sm': 'هاتف كبير',
            'md': 'تابلت',
            'lg': 'سطح مكتب',
            'xl': 'شاشة كبيرة'
        }
        
        size_name = size_names.get(size_category, 'غير معروف')
        self.setWindowTitle(f"🎨 عرض توضيحي للنظام المتجاوب - {size_name}")
        
    def on_theme_changed(self, theme_name):
        """معالج تغيير الثيم"""
        print(f"تم تغيير الثيم إلى: {theme_name}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ResponsiveDemoWindow()
    window.show()
    
    print("🎨 تم تشغيل العرض التوضيحي للنظام المتجاوب")
    print("📱 جرب تغيير حجم النافذة أو الثيم لرؤية التحسينات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("responsive_demo.py", "w", encoding="utf-8") as f:
            f.write(demo_content)
        print("✅ تم إنشاء العرض التوضيحي بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء العرض التوضيحي: {e}")

def main():
    """الدالة الرئيسية للتكامل النهائي"""
    print("🚀 بدء التكامل النهائي للنظام المتجاوب...")
    print("=" * 60)
    
    # دمج النظام في الملف الرئيسي
    integrate_responsive_system()
    
    # إضافة دوال التجاوب للصفحات
    add_responsive_methods_to_pages()
    
    # إنشاء العرض التوضيحي
    create_responsive_demo()
    
    print("=" * 60)
    print("🎉 تم الانتهاء من التكامل النهائي!")
    print("📱 النظام المتجاوب جاهز للاستخدام على جميع الأحجام")
    print("🎨 يمكنك تشغيل responsive_demo.py لرؤية العرض التوضيحي")

if __name__ == "__main__":
    main()
