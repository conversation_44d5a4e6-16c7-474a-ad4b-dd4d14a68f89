import sys
from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QLineEdit, QDoubleSpinBox, QGroupBox, QFormLayout, 
                             QMessageBox, QProgressBar, QFrame, QScrollArea,
                             QGridLayout, QDateEdit, QTextEdit, QCheckBox,
                             QSizePolicy, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter
from responsive_utils import ResponsiveUtils
from responsive_grid import ResponsiveContainer

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

class AddGoalDialog(QDialog):
    """نافذة إضافة هدف مالي جديد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة هدف مالي جديد")
        self.setModal(True)
        self.resize(400, 300)
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم الهدف
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("مثال: شراء سيارة جديدة")
        form_layout.addRow("اسم الهدف:", self.name_edit)
        
        # المبلغ المطلوب
        self.target_amount = QDoubleSpinBox()
        self.target_amount.setRange(1, 9999999)
        self.target_amount.setSuffix(" ريال")
        self.target_amount.setDecimals(2)
        form_layout.addRow("المبلغ المطلوب:", self.target_amount)
        
        # التاريخ المستهدف
        self.target_date = QDateEdit()
        self.target_date.setDate(QDate.currentDate().addMonths(12))
        self.target_date.setCalendarPopup(True)
        form_layout.addRow("التاريخ المستهدف:", self.target_date)
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف اختياري للهدف...")
        form_layout.addRow("الوصف:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def get_goal_data(self):
        """الحصول على بيانات الهدف"""
        return {
            'name': self.name_edit.text().strip(),
            'target_amount': self.target_amount.value(),
            'target_date': self.target_date.date().toString('yyyy-MM-dd'),
            'description': self.description_edit.toPlainText().strip()
        }

class GoalsPage(QWidget):
    """صفحة الأهداف المالية"""
    
    goal_updated = pyqtSignal()
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.init_ui()
        self.load_goals()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الصفحة
        title = QLabel("الأهداف المالية")
        title.setObjectName("page_title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        main_content = QHBoxLayout()
        
        # الجانب الأيسر - قائمة الأهداف
        left_panel = self.create_goals_list_panel()
        main_content.addWidget(left_panel, 2)
        
        # الجانب الأيمن - إحصائيات الأهداف
        right_panel = self.create_goals_stats_panel()
        main_content.addWidget(right_panel, 1)
        
        layout.addLayout(main_content)

        # حفظ مرجع للتخطيط الرئيسي للتجاوب
        self.main_content_layout = main_content


    def update_theme(self, theme_name):
        """تحديث ثيم الصفحة"""
        try:
            # تحديث ألوان أشرطة التقدم
            self.update_progress_bars_theme(theme_name)
        except Exception as e:
            print(f"خطأ في تحديث ثيم الأهداف: {e}")
    
    def update_progress_bars_theme(self, theme_name):
        """تحديث ثيم أشرطة التقدم"""
        try:
            # إعادة تحميل الأهداف بالثيم الجديد
            self.load_goals()
        except Exception as e:
            print(f"خطأ في تحديث ثيم أشرطة التقدم: {e}")

    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            if size_category in ['xs', 'sm']:
                # ترتيب عمودي للهواتف
                self.apply_mobile_layout()
            else:
                # ترتيب أفقي للشاشات الكبيرة
                self.apply_desktop_layout()
        except Exception as e:
            print(f"خطأ في تحديث تخطيط الأهداف: {e}")

def apply_mobile_layout(self):
        """تطبيق تخطيط الهاتف المحسن"""
        try:
            # تحسينات خاصة بالهاتف
            ResponsiveUtils.apply_responsive_font(self, 14, 'sm')
            ResponsiveUtils.apply_responsive_margins(self, 8, 'sm')
            
            # تحسين قائمة الأهداف للهاتف
            if hasattr(self, 'goals_list'):
                ResponsiveUtils.optimize_for_mobile(self.goals_list, 'sm')
                
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط الهاتف: {e}")

(self):
        """تطبيق تخطيط سطح المكتب المحسن"""
        try:
            # تحسينات خاصة بسطح المكتب
            ResponsiveUtils.apply_responsive_font(self, 14, 'lg')
            ResponsiveUtils.apply_responsive_margins(self, 20, 'lg')
            
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط سطح المكتب: {e}")
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setObjectName("toolbar")
        layout = QHBoxLayout(toolbar)
        
        # زر إضافة هدف جديد
        add_btn = QPushButton("إضافة هدف جديد")
        add_btn.setObjectName("primary_button")
        add_btn.clicked.connect(self.add_new_goal)
        layout.addWidget(add_btn)
        
        layout.addStretch()
        
        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.load_goals)
        layout.addWidget(refresh_btn)
        
        return toolbar
        
    def create_goals_list_panel(self):
        """إنشاء لوحة قائمة الأهداف"""
        group = QGroupBox("أهدافي المالية")
        layout = QVBoxLayout(group)
        
        # منطقة التمرير للأهداف
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.goals_widget = QWidget()
        self.goals_layout = QVBoxLayout(self.goals_widget)
        self.goals_layout.setSpacing(15)
        
        scroll.setWidget(self.goals_widget)
        layout.addWidget(scroll)
        
        return group
        
    def create_goals_stats_panel(self):
        """إنشاء لوحة إحصائيات الأهداف"""
        group = QGroupBox("إحصائيات الأهداف")
        layout = QVBoxLayout(group)
        
        # بطاقات الإحصائيات
        stats_layout = QVBoxLayout()
        
        # إجمالي الأهداف
        self.total_goals_card = self.create_stat_card("إجمالي الأهداف", "0", "#3498db")
        stats_layout.addWidget(self.total_goals_card)
        
        # الأهداف المكتملة
        self.completed_goals_card = self.create_stat_card("الأهداف المكتملة", "0", "#27ae60")
        stats_layout.addWidget(self.completed_goals_card)
        
        # إجمالي المبلغ المطلوب
        self.total_amount_card = self.create_stat_card("إجمالي المبلغ المطلوب", "0 ريال", "#e74c3c")
        stats_layout.addWidget(self.total_amount_card)
        
        # إجمالي المبلغ المحقق
        self.achieved_amount_card = self.create_stat_card("إجمالي المبلغ المحقق", "0 ريال", "#f39c12")
        stats_layout.addWidget(self.achieved_amount_card)
        
        layout.addLayout(stats_layout)
        
        # الرسم البياني
        self.create_goals_chart(layout)
        
        layout.addStretch()
        
        return group
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("stat_card")
        card.setFrameStyle(QFrame.StyledPanel)
        card.setMaximumHeight(80)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setObjectName("stat_title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setObjectName("stat_value")
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 18px;")
        layout.addWidget(value_label)
        
        return card
        
    def create_goals_chart(self, parent_layout):
        """إنشاء الرسم البياني للأهداف"""
        chart_frame = QFrame()
        chart_frame.setMaximumHeight(250)
        chart_layout = QVBoxLayout(chart_frame)
        
        chart_title = QLabel("تقدم الأهداف")
        chart_title.setAlignment(Qt.AlignCenter)
        chart_title.setObjectName("chart_title")
        chart_layout.addWidget(chart_title)
        
        # إنشاء الرسم البياني
        self.figure = Figure(figsize=(6, 3))
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)
        
        parent_layout.addWidget(chart_frame)
        
    def add_new_goal(self):
        """إضافة هدف جديد"""
        dialog = AddGoalDialog(self)
        
        if dialog.exec_() == QDialog.Accepted:
            goal_data = dialog.get_goal_data()
            
            if not goal_data['name'] or goal_data['target_amount'] <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
                
            success = self.db.add_financial_goal(
                goal_data['name'],
                goal_data['target_amount'],
                goal_data['target_date'],
                goal_data['description']
            )
            
            if success:
                QMessageBox.information(self, "نجح", "تم إضافة الهدف بنجاح")
                self.load_goals()
                self.goal_updated.emit()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إضافة الهدف")
                
    def load_goals(self):
        """تحميل الأهداف"""
        # مسح الأهداف الموجودة
        for i in reversed(range(self.goals_layout.count())):
            child = self.goals_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        # جلب الأهداف من قاعدة البيانات
        goals = self.db.get_financial_goals()
        
        if not goals:
            no_goals_label = QLabel("لا توجد أهداف مالية حتى الآن")
            no_goals_label.setAlignment(Qt.AlignCenter)
            no_goals_label.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 40px;")
            self.goals_layout.addWidget(no_goals_label)
        else:
            for goal in goals:
                goal_card = self.create_goal_card(goal)
                self.goals_layout.addWidget(goal_card)
                
        self.goals_layout.addStretch()
        
        # تحديث الإحصائيات
        self.update_stats(goals)
        self.update_goals_chart(goals)
        
    def create_goal_card(self, goal):
        """إنشاء بطاقة هدف"""
        card = QFrame()
        card.setObjectName("goal_card")
        card.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(10)
        
        # رأس البطاقة
        header_layout = QHBoxLayout()
        
        # اسم الهدف
        name_label = QLabel(goal['name'])
        name_label.setObjectName("goal_name")
        name_label.setWordWrap(True)
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        # حالة الهدف
        if goal['progress_percentage'] >= 100:
            status_label = QLabel("مكتمل ✓")
            status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            status_label = QLabel("قيد التنفيذ")
            status_label.setStyleSheet("color: #f39c12; font-weight: bold;")
        header_layout.addWidget(status_label)
        
        layout.addLayout(header_layout)
        
        # شريط التقدم
        progress = QProgressBar()
        progress.setMaximum(100)
        progress.setValue(min(int(goal['progress_percentage']), 100))
        progress.setFormat(f"{goal['progress_percentage']:.1f}%")
        
        # تلوين شريط التقدم
        if goal['progress_percentage'] >= 100:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")
        elif goal['progress_percentage'] >= 75:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
        else:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #3498db; }")
            
        layout.addWidget(progress)
        
        # تفاصيل الهدف
        details_layout = QGridLayout()
        
        details_layout.addWidget(QLabel("المبلغ المطلوب:"), 0, 0)
        details_layout.addWidget(QLabel(f"{goal['target_amount']:.2f} ريال"), 0, 1)
        
        details_layout.addWidget(QLabel("المبلغ المحقق:"), 1, 0)
        current_label = QLabel(f"{goal['current_amount']:.2f} ريال")
        current_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        details_layout.addWidget(current_label, 1, 1)
        
        remaining = goal['target_amount'] - goal['current_amount']
        details_layout.addWidget(QLabel("المبلغ المتبقي:"), 2, 0)
        remaining_label = QLabel(f"{remaining:.2f} ريال")
        if remaining <= 0:
            remaining_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        else:
            remaining_label.setStyleSheet("color: #e74c3c;")
        details_layout.addWidget(remaining_label, 2, 1)
        
        if goal['target_date']:
            details_layout.addWidget(QLabel("التاريخ المستهدف:"), 3, 0)
            details_layout.addWidget(QLabel(goal['target_date']), 3, 1)
        
        layout.addLayout(details_layout)
        
        # الوصف
        if goal['description']:
            desc_label = QLabel(goal['description'])
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #7f8c8d; font-style: italic; margin-top: 5px;")
            layout.addWidget(desc_label)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        # زر إضافة مبلغ
        add_amount_btn = QPushButton("إضافة مبلغ")
        add_amount_btn.setObjectName("primary_button")
        add_amount_btn.clicked.connect(lambda: self.add_amount_to_goal(goal['id']))
        buttons_layout.addWidget(add_amount_btn)
        
        # زر تعديل
        edit_btn = QPushButton("تعديل")
        edit_btn.clicked.connect(lambda: self.edit_goal(goal['id']))
        buttons_layout.addWidget(edit_btn)
        
        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setObjectName("danger_button")
        delete_btn.clicked.connect(lambda: self.delete_goal(goal['id']))
        buttons_layout.addWidget(delete_btn)
        
        layout.addLayout(buttons_layout)
        
        return card
        
    def add_amount_to_goal(self, goal_id):
        """إضافة مبلغ لهدف"""
        from PyQt5.QtWidgets import QInputDialog

        amount, ok = QInputDialog.getDouble(self, "إضافة مبلغ", "المبلغ المراد إضافته:", 0, 0, 999999, 2)

        if ok and amount > 0:
            success = self.db.update_goal_progress(goal_id, amount)

            if success:
                QMessageBox.information(self, "نجح", f"تم إضافة {amount:.2f} ريال للهدف")
                self.load_goals()
                self.goal_updated.emit()

                # إضافة تنبيه إذا تم تحقيق الهدف
                goals = self.db.get_financial_goals()
                for goal in goals:
                    if goal['id'] == goal_id and goal['progress_percentage'] >= 100:
                        self.db.add_notification(
                            "🎉 تهانينا!",
                            f"لقد حققت هدفك: {goal['name']}",
                            "success"
                        )
                        break
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تحديث الهدف")
                
    def edit_goal(self, goal_id):
        """تعديل هدف"""
        QMessageBox.information(self, "قريباً", "ميزة التعديل ستكون متاحة قريباً")
        
    def delete_goal(self, goal_id):
        """حذف هدف"""
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل أنت متأكد من حذف هذا الهدف؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                with self.db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('DELETE FROM financial_goals WHERE id = ?', (goal_id,))
                    conn.commit()
                    
                QMessageBox.information(self, "تم الحذف", "تم حذف الهدف بنجاح")
                self.load_goals()
                self.goal_updated.emit()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الهدف: {str(e)}")
                
    def update_stats(self, goals):
        """تحديث الإحصائيات"""
        total_goals = len(goals)
        completed_goals = len([g for g in goals if g['progress_percentage'] >= 100])
        total_amount = sum(g['target_amount'] for g in goals)
        achieved_amount = sum(g['current_amount'] for g in goals)
        
        # تحديث البطاقات
        self.total_goals_card.findChild(QLabel, "stat_value").setText(str(total_goals))
        self.completed_goals_card.findChild(QLabel, "stat_value").setText(str(completed_goals))
        self.total_amount_card.findChild(QLabel, "stat_value").setText(f"{total_amount:.0f} ريال")
        self.achieved_amount_card.findChild(QLabel, "stat_value").setText(f"{achieved_amount:.0f} ريال")
        
    def update_goals_chart(self, goals):
        """تحديث الرسم البياني"""
        self.figure.clear()
        
        if not goals:
            return
            
        ax = self.figure.add_subplot(111)
        
        # تجميع الأهداف حسب الحالة
        completed = len([g for g in goals if g['progress_percentage'] >= 100])
        in_progress = len([g for g in goals if 0 < g['progress_percentage'] < 100])
        not_started = len([g for g in goals if g['progress_percentage'] == 0])
        
        labels = []
        sizes = []
        colors = []
        
        if completed > 0:
            labels.append(f'مكتملة ({completed})')
            sizes.append(completed)
            colors.append('#27ae60')
            
        if in_progress > 0:
            labels.append(f'قيد التنفيذ ({in_progress})')
            sizes.append(in_progress)
            colors.append('#f39c12')
            
        if not_started > 0:
            labels.append(f'لم تبدأ ({not_started})')
            sizes.append(not_started)
            colors.append('#e74c3c')
        
        if sizes:
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax.set_title('توزيع الأهداف حسب الحالة')
        
        self.figure.tight_layout()
        self.canvas.draw()

# استيراد QInputDialog
from PyQt5.QtWidgets import QInputDialog
