#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المتجاوب
يختبر جميع أحجام الشاشات والتحسينات المتجاوبة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QPushButton, QLabel, QFrame, QGridLayout, QScrollArea
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from responsive_manager import ResponsiveManager
from responsive_utils import ResponsiveUtils, ResponsiveBreakpoints

class ResponsiveTestWindow(QMainWindow):
    """نافذة اختبار التصميم المتجاوب"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_responsive_system()
        self.create_test_content()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار النظام المتجاوب - إدارة المصاريف")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(320, 480)  # أصغر حجم للهاتف
        
        # تعيين اتجاه النص
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تحميل الأنماط
        self.load_styles()
        
    def load_styles(self):
        """تحميل ملف الأنماط"""
        try:
            with open("styles.qss", "r", encoding="utf-8") as f:
                self.setStyleSheet(f.read())
        except FileNotFoundError:
            print("ملف الأنماط غير موجود")
    
    def setup_responsive_system(self):
        """إعداد النظام المتجاوب"""
        self.responsive_manager = ResponsiveManager(self)
        self.responsive_manager.size_changed.connect(self.on_size_changed)
        self.responsive_manager.orientation_changed.connect(self.on_orientation_changed)
    
    def create_test_content(self):
        """إنشاء محتوى الاختبار"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.create_main_content()
        main_layout.addWidget(self.main_content, 1)
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # شعار التطبيق
        logo = QLabel("💰 اختبار التجاوب")
        logo.setAlignment(Qt.AlignCenter)
        logo.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 30px 20px;
                background-color: #2c3e50;
                border-bottom: 2px solid #34495e;
            }
        """)
        layout.addWidget(logo)
        
        # أزرار التنقل
        nav_buttons = [
            "📊 لوحة المعلومات",
            "💰 إضافة مصروف", 
            "📋 إدارة المصاريف",
            "📈 الميزانيات",
            "🎯 الأهداف المالية",
            "🔔 التنبيهات",
            "🤖 المساعد الذكي"
        ]
        
        for text in nav_buttons:
            btn = QPushButton(text)
            btn.setCheckable(True)
            layout.addWidget(btn)
        
        layout.addStretch()
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QFrame()
        self.main_content.setObjectName("main_content")
        
        layout = QVBoxLayout(self.main_content)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("🧪 اختبار النظام المتجاوب")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات الحجم الحالي
        self.size_info = QLabel("الحجم الحالي: غير محدد")
        self.size_info.setAlignment(Qt.AlignCenter)
        self.size_info.setStyleSheet("font-size: 16px; color: #3498db; margin: 10px;")
        layout.addWidget(self.size_info)
        
        # بطاقات الاختبار
        self.create_test_cards(layout)
        
        # أزرار الاختبار
        self.create_test_buttons(layout)
        
        # جدول الاختبار
        self.create_test_table(layout)
        
    def create_test_cards(self, parent_layout):
        """إنشاء بطاقات الاختبار"""
        cards_frame = QFrame()
        self.cards_layout = QGridLayout(cards_frame)
        self.cards_layout.setSpacing(15)
        
        # إنشاء 6 بطاقات اختبار
        self.test_cards = []
        for i in range(6):
            card = QFrame()
            card.setObjectName("card")
            
            card_layout = QVBoxLayout(card)
            
            # عنوان البطاقة
            title = QLabel(f"بطاقة اختبار {i+1}")
            title.setObjectName("card_title")
            title.setAlignment(Qt.AlignCenter)
            card_layout.addWidget(title)
            
            # محتوى البطاقة
            content = QLabel(f"محتوى تجريبي للبطاقة رقم {i+1}")
            content.setAlignment(Qt.AlignCenter)
            content.setStyleSheet("color: #7f8c8d; padding: 10px;")
            card_layout.addWidget(content)
            
            # زر في البطاقة
            btn = QPushButton(f"زر {i+1}")
            btn.setObjectName("primary_button")
            card_layout.addWidget(btn)
            
            self.test_cards.append(card)
            
            # ترتيب البطاقات في شبكة 3x2
            row = i // 3
            col = i % 3
            self.cards_layout.addWidget(card, row, col)
        
        parent_layout.addWidget(cards_frame)
        
    def create_test_buttons(self, parent_layout):
        """إنشاء أزرار الاختبار"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # أزرار بأحجام مختلفة
        btn_configs = [
            ("زر صغير", "btn-sm"),
            ("زر عادي", ""),
            ("زر كبير", "btn-lg"),
            ("زر نجاح", "btn-success"),
            ("زر خطر", "btn-danger"),
            ("زر تحذير", "btn-warning")
        ]
        
        for text, class_name in btn_configs:
            btn = QPushButton(text)
            if class_name:
                btn.setProperty("class", class_name)
            buttons_layout.addWidget(btn)
        
        parent_layout.addWidget(buttons_frame)
        
    def create_test_table(self, parent_layout):
        """إنشاء جدول الاختبار"""
        from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem
        
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["العمود 1", "العمود 2", "العمود 3", "العمود 4"])
        
        # ملء الجدول ببيانات تجريبية
        for row in range(5):
            for col in range(4):
                item = QTableWidgetItem(f"خلية {row+1}-{col+1}")
                table.setItem(row, col, item)
        
        parent_layout.addWidget(table)
        
    def on_size_changed(self, size_category):
        """معالج تغيير الحجم"""
        size_names = {
            'xs': 'هاتف صغير (أقل من 480px)',
            'sm': 'هاتف كبير (480px - 768px)', 
            'md': 'تابلت (768px - 1024px)',
            'lg': 'سطح مكتب (1024px - 1440px)',
            'xl': 'شاشة كبيرة (أكثر من 1440px)'
        }
        
        size_name = size_names.get(size_category, 'غير معروف')
        self.size_info.setText(f"الحجم الحالي: {size_name}")
        
        # تطبيق التحسينات المتجاوبة
        self.apply_responsive_optimizations(size_category)
        
        print(f"تم تغيير الحجم إلى: {size_category} - {size_name}")
        
    def on_orientation_changed(self, orientation):
        """معالج تغيير الاتجاه"""
        print(f"تم تغيير الاتجاه إلى: {orientation}")
        
    def apply_responsive_optimizations(self, size_category):
        """تطبيق التحسينات المتجاوبة"""
        try:
            # إعادة ترتيب البطاقات
            if hasattr(self, 'cards_layout') and hasattr(self, 'test_cards'):
                if size_category == 'xs':
                    # عمود واحد للهواتف الصغيرة
                    ResponsiveUtils.rearrange_grid_layout(
                        self.cards_layout, self.test_cards, 1
                    )
                elif size_category == 'sm':
                    # عمودين للهواتف الكبيرة
                    ResponsiveUtils.rearrange_grid_layout(
                        self.cards_layout, self.test_cards, 2
                    )
                else:
                    # ثلاثة أعمدة للشاشات الكبيرة
                    ResponsiveUtils.rearrange_grid_layout(
                        self.cards_layout, self.test_cards, 3
                    )
            
            # تحديث الشريط الجانبي
            if hasattr(self, 'sidebar'):
                if size_category in ['xs', 'sm']:
                    self.sidebar.setVisible(False)
                else:
                    self.sidebar.setVisible(True)
                    
        except Exception as e:
            print(f"خطأ في تطبيق التحسينات: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط الافتراضي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # تعيين اتجاه التطبيق
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = ResponsiveTestWindow()
    window.show()
    
    # رسالة ترحيب
    print("🧪 تم تشغيل اختبار النظام المتجاوب")
    print("📱 جرب تغيير حجم النافذة لرؤية التحسينات المتجاوبة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
