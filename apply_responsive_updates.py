#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق التحديثات المتجاوبة على جميع صفحات التطبيق
يحدث جميع الصفحات لتدعم النظام المتجاوب الجديد
"""

import os
import sys
from PyQt5.QtWidgets import QApplication
from responsive_utils import ResponsiveUtils
from responsive_grid import ResponsiveGridWidget, ResponsiveContainer

def update_dashboard_page():
    """تحديث صفحة لوحة المعلومات"""
    print("🔄 تحديث صفحة لوحة المعلومات...")
    
    # قراءة الملف الحالي
    try:
        with open("dashboard_page.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # إضافة استيرادات جديدة إذا لم تكن موجودة
        if "from responsive_utils import ResponsiveUtils" not in content:
            import_line = "from responsive_utils import ResponsiveUtils\nfrom responsive_grid import ResponsiveGridWidget\n"
            content = content.replace(
                "import matplotlib.pyplot as plt",
                f"import matplotlib.pyplot as plt\n{import_line}"
            )
        
        # إضافة دوال التجاوب المحسنة
        responsive_methods = '''
    def apply_responsive_optimizations(self, size_category):
        """تطبيق التحسينات المتجاوبة المتقدمة"""
        try:
            # تطبيق أحجام الخطوط المتجاوبة
            ResponsiveUtils.apply_responsive_font(self, 14, size_category)
            
            # تطبيق الهوامش المتجاوبة
            ResponsiveUtils.apply_responsive_margins(self, 20, size_category)
            
            # تحديث البطاقات
            if hasattr(self, 'stats_layout'):
                ResponsiveUtils.apply_responsive_spacing(self.stats_layout, 15, size_category)
                
                # إعادة ترتيب البطاقات
                cards = [self.total_card, self.count_card, self.avg_card, 
                        self.max_card, self.budget_card, self.goals_card]
                
                if size_category == 'xs':
                    ResponsiveUtils.rearrange_grid_layout(self.stats_layout, cards, 1)
                elif size_category == 'sm':
                    ResponsiveUtils.rearrange_grid_layout(self.stats_layout, cards, 2)
                else:
                    ResponsiveUtils.rearrange_grid_layout(self.stats_layout, cards, 3)
            
            # تحديث الرسوم البيانية
            self.update_charts_for_size(size_category)
            
        except Exception as e:
            print(f"خطأ في تطبيق التحسينات: {e}")
    
    def update_charts_for_size(self, size_category):
        """تحديث الرسوم البيانية حسب الحجم"""
        try:
            if hasattr(self, 'pie_chart') and hasattr(self, 'line_chart'):
                if size_category in ['xs', 'sm']:
                    # أحجام صغيرة للهاتف
                    self.pie_chart.setFixedSize(250, 200)
                    self.line_chart.setFixedSize(250, 200)
                elif size_category == 'md':
                    # أحجام متوسطة للتابلت
                    self.pie_chart.setFixedSize(300, 250)
                    self.line_chart.setFixedSize(300, 250)
                else:
                    # أحجام كبيرة لسطح المكتب
                    self.pie_chart.setFixedSize(400, 300)
                    self.line_chart.setFixedSize(400, 300)
        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")
'''
        
        # إضافة الدوال الجديدة قبل النهاية
        if "def apply_responsive_optimizations" not in content:
            content = content.replace(
                "    def refresh_data(self):",
                f"{responsive_methods}\n    def refresh_data(self):"
            )
        
        # حفظ الملف المحدث
        with open("dashboard_page.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        print("✅ تم تحديث صفحة لوحة المعلومات بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث صفحة لوحة المعلومات: {e}")

def update_budget_page():
    """تحديث صفحة الميزانيات"""
    print("🔄 تحديث صفحة الميزانيات...")
    
    try:
        with open("budget_page.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # إضافة استيرادات جديدة
        if "from responsive_utils import ResponsiveUtils" not in content:
            import_line = "from responsive_utils import ResponsiveUtils\nfrom layout_manager import ResponsivePageLayout\n"
            content = content.replace(
                "from PyQt5.QtGui import QFont, QPalette, QColor",
                f"from PyQt5.QtGui import QFont, QPalette, QColor\n{import_line}"
            )
        
        # تحسين دوال التجاوب الموجودة
        improved_methods = '''
    def apply_mobile_layout(self):
        """تطبيق تخطيط الهاتف المحسن"""
        try:
            # تحويل التخطيط إلى عمودي
            if hasattr(self, 'main_content_layout'):
                # تطبيق تحسينات الهاتف
                ResponsiveUtils.apply_responsive_margins(self, 10, 'sm')
                ResponsiveUtils.apply_responsive_spacing(self.main_content_layout, 8, 'sm')
                
                # إخفاء عناصر غير ضرورية
                if hasattr(self, 'budget_chart'):
                    self.budget_chart.setVisible(False)
                    
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط الهاتف: {e}")
    
    def apply_desktop_layout(self):
        """تطبيق تخطيط سطح المكتب المحسن"""
        try:
            # تطبيق إعدادات سطح المكتب
            if hasattr(self, 'main_content_layout'):
                ResponsiveUtils.apply_responsive_margins(self, 20, 'lg')
                ResponsiveUtils.apply_responsive_spacing(self.main_content_layout, 15, 'lg')
                
                # إظهار جميع العناصر
                if hasattr(self, 'budget_chart'):
                    self.budget_chart.setVisible(True)
                    
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط سطح المكتب: {e}")
'''
        
        # استبدال الدوال الموجودة
        content = content.replace(
            "    def apply_mobile_layout(self):\n        \"\"\"تطبيق تخطيط الهاتف\"\"\"\n        # تحسينات خاصة بالهاتف\n        pass",
            improved_methods.split("def apply_desktop_layout")[0].strip()
        )
        
        content = content.replace(
            "    def apply_desktop_layout(self):\n        \"\"\"تطبيق تخطيط سطح المكتب\"\"\"\n        # تحسينات خاصة بسطح المكتب\n        pass",
            improved_methods.split("def apply_desktop_layout")[1].strip()
        )
        
        # حفظ الملف
        with open("budget_page.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        print("✅ تم تحديث صفحة الميزانيات بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث صفحة الميزانيات: {e}")

def update_goals_page():
    """تحديث صفحة الأهداف"""
    print("🔄 تحديث صفحة الأهداف...")
    
    try:
        with open("goals_page.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # إضافة استيرادات جديدة
        if "from responsive_utils import ResponsiveUtils" not in content:
            import_line = "from responsive_utils import ResponsiveUtils\nfrom responsive_grid import ResponsiveContainer\n"
            content = content.replace(
                "from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter",
                f"from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter\n{import_line}"
            )
        
        # تحسين دوال التجاوب
        improved_methods = '''
    def apply_mobile_layout(self):
        """تطبيق تخطيط الهاتف المحسن"""
        try:
            # تحسينات خاصة بالهاتف
            ResponsiveUtils.apply_responsive_font(self, 14, 'sm')
            ResponsiveUtils.apply_responsive_margins(self, 8, 'sm')
            
            # تحسين قائمة الأهداف للهاتف
            if hasattr(self, 'goals_list'):
                ResponsiveUtils.optimize_for_mobile(self.goals_list, 'sm')
                
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط الهاتف: {e}")
    
    def apply_desktop_layout(self):
        """تطبيق تخطيط سطح المكتب المحسن"""
        try:
            # تحسينات خاصة بسطح المكتب
            ResponsiveUtils.apply_responsive_font(self, 14, 'lg')
            ResponsiveUtils.apply_responsive_margins(self, 20, 'lg')
            
        except Exception as e:
            print(f"خطأ في تطبيق تخطيط سطح المكتب: {e}")
'''
        
        # استبدال الدوال الموجودة
        content = content.replace(
            "    def apply_mobile_layout(self):\n        \"\"\"تطبيق تخطيط الهاتف\"\"\"\n        # تحسينات خاصة بالهاتف\n        pass",
            improved_methods.split("def apply_desktop_layout")[0].strip()
        )
        
        content = content.replace(
            "    def apply_desktop_layout(self):\n        \"\"\"تطبيق تخطيط سطح المكتب\"\"\"\n        # تحسينات خاصة بسطح المكتب\n        pass",
            improved_methods.split("def apply_desktop_layout")[1].strip()
        )
        
        # حفظ الملف
        with open("goals_page.py", "w", encoding="utf-8") as f:
            f.write(content)
            
        print("✅ تم تحديث صفحة الأهداف بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث صفحة الأهداف: {e}")

def update_other_pages():
    """تحديث باقي الصفحات"""
    pages = [
        "add_expense_page.py",
        "manage_expenses_page.py", 
        "notifications_page.py",
        "ai_insights_page.py"
    ]
    
    for page_file in pages:
        if os.path.exists(page_file):
            print(f"🔄 تحديث {page_file}...")
            
            try:
                with open(page_file, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # إضافة استيرادات أساسية
                if "from responsive_utils import ResponsiveUtils" not in content:
                    import_line = "from responsive_utils import ResponsiveUtils\n"
                    
                    # البحث عن مكان مناسب للإضافة
                    if "from PyQt5.QtGui import" in content:
                        content = content.replace(
                            "from PyQt5.QtGui import",
                            f"{import_line}from PyQt5.QtGui import"
                        )
                    elif "from PyQt5.QtCore import" in content:
                        content = content.replace(
                            "from PyQt5.QtCore import",
                            f"{import_line}from PyQt5.QtCore import"
                        )
                
                # إضافة دالة تحديث أساسية
                if "def update_responsive_layout" not in content:
                    responsive_method = '''
    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            # تطبيق تحسينات أساسية
            ResponsiveUtils.apply_responsive_font(self, 14, size_category)
            ResponsiveUtils.apply_responsive_margins(self, 15, size_category)
            
            # تحسينات خاصة بالهاتف
            if size_category in ['xs', 'sm']:
                ResponsiveUtils.optimize_for_mobile(self, size_category)
                
        except Exception as e:
            print(f"خطأ في تحديث التخطيط: {e}")
'''
                    
                    # إضافة الدالة قبل النهاية
                    if "class " in content and "def __init__" in content:
                        # البحث عن آخر دالة في الكلاس
                        lines = content.split('\n')
                        insert_index = -1
                        
                        for i, line in enumerate(lines):
                            if line.strip().startswith('def ') and not line.strip().startswith('def __'):
                                insert_index = i
                        
                        if insert_index > 0:
                            lines.insert(insert_index + 1, responsive_method)
                            content = '\n'.join(lines)
                
                # حفظ الملف المحدث
                with open(page_file, "w", encoding="utf-8") as f:
                    f.write(content)
                    
                print(f"✅ تم تحديث {page_file} بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في تحديث {page_file}: {e}")

def main():
    """الدالة الرئيسية لتطبيق التحديثات"""
    print("🚀 بدء تطبيق التحديثات المتجاوبة على جميع الصفحات...")
    print("=" * 60)
    
    # تحديث الصفحات الرئيسية
    update_dashboard_page()
    update_budget_page()
    update_goals_page()
    
    # تحديث باقي الصفحات
    update_other_pages()
    
    print("=" * 60)
    print("🎉 تم الانتهاء من تطبيق جميع التحديثات المتجاوبة!")
    print("📱 التطبيق الآن يدعم جميع أحجام الشاشات بشكل احترافي")

if __name__ == "__main__":
    main()
