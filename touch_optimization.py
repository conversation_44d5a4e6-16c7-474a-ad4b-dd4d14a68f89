#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسينات التفاعل للأجهزة اللمسية
يحسن تجربة اللمس والتنقل للهواتف والتابلت
"""

from PyQt5.QtWidgets import (QWidget, QGestureEvent, QTapGesture, QSwipeGesture,
                             QPinchGesture, QScrollArea, QScroller, QAbstractScrollArea)
from PyQt5.QtCore import Qt, QObject, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QTouchEvent, QMouseEvent

class TouchOptimizer(QObject):
    """محسن التفاعل اللمسي"""
    
    gesture_detected = pyqtSignal(str, object)  # نوع الإيماءة والبيانات
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.touch_enabled = True
        self.gesture_enabled = True
        self.scroll_optimization = True
        
    def enable_touch_for_widget(self, widget):
        """تفعيل اللمس لويدجت"""
        if not self.touch_enabled:
            return
            
        # تفعيل اللمس
        widget.setAttribute(Qt.WA_AcceptTouchEvents, True)
        
        # تفعيل الإيماءات
        if self.gesture_enabled:
            widget.grabGesture(Qt.TapGesture)
            widget.grabGesture(Qt.SwipeGesture)
            widget.grabGesture(Qt.PinchGesture)
            
        # تحسين التمرير
        if self.scroll_optimization and isinstance(widget, QAbstractScrollArea):
            self.optimize_scrolling(widget)
            
    def optimize_scrolling(self, scroll_area):
        """تحسين التمرير للأجهزة اللمسية"""
        try:
            # تفعيل التمرير الحركي
            QScroller.grabGesture(scroll_area, QScroller.LeftMouseButtonGesture)
            
            # إعدادات التمرير
            scroller = QScroller.scroller(scroll_area)
            if scroller:
                properties = scroller.scrollerProperties()
                
                # تحسين سرعة التمرير
                properties.setScrollMetric(QScroller.DecelerationFactor, 0.35)
                properties.setScrollMetric(QScroller.MaximumVelocity, 0.5)
                properties.setScrollMetric(QScroller.OvershootDragResistanceFactor, 0.33)
                properties.setScrollMetric(QScroller.OvershootScrollDistanceFactor, 0.33)
                
                scroller.setScrollerProperties(properties)
                
        except Exception as e:
            print(f"خطأ في تحسين التمرير: {e}")
    
    def handle_gesture_event(self, widget, event):
        """معالجة أحداث الإيماءات"""
        if not self.gesture_enabled:
            return False
            
        try:
            # معالجة النقر
            tap = event.gesture(Qt.TapGesture)
            if tap and tap.state() == Qt.GestureFinished:
                self.handle_tap_gesture(widget, tap)
                return True
                
            # معالجة السحب
            swipe = event.gesture(Qt.SwipeGesture)
            if swipe and swipe.state() == Qt.GestureFinished:
                self.handle_swipe_gesture(widget, swipe)
                return True
                
            # معالجة القرص (التكبير/التصغير)
            pinch = event.gesture(Qt.PinchGesture)
            if pinch and pinch.state() == Qt.GestureFinished:
                self.handle_pinch_gesture(widget, pinch)
                return True
                
        except Exception as e:
            print(f"خطأ في معالجة الإيماءة: {e}")
            
        return False
    
    def handle_tap_gesture(self, widget, gesture):
        """معالجة إيماءة النقر"""
        position = gesture.position()
        self.gesture_detected.emit("tap", {
            'widget': widget,
            'position': position,
            'gesture': gesture
        })
        
    def handle_swipe_gesture(self, widget, gesture):
        """معالجة إيماءة السحب"""
        direction = gesture.horizontalDirection()
        self.gesture_detected.emit("swipe", {
            'widget': widget,
            'direction': direction,
            'gesture': gesture
        })
        
    def handle_pinch_gesture(self, widget, gesture):
        """معالجة إيماءة القرص"""
        scale_factor = gesture.scaleFactor()
        self.gesture_detected.emit("pinch", {
            'widget': widget,
            'scale': scale_factor,
            'gesture': gesture
        })

class MobileNavigationManager(QObject):
    """مدير التنقل للهاتف"""
    
    navigation_changed = pyqtSignal(str)  # اتجاه التنقل
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.sidebar_visible = False
        self.navigation_history = []
        self.current_page = None
        
    def setup_mobile_navigation(self):
        """إعداد التنقل للهاتف"""
        # إخفاء الشريط الجانبي افتراضياً
        if hasattr(self.main_window, 'sidebar'):
            self.main_window.sidebar.setVisible(False)
            self.sidebar_visible = False
            
        # إضافة زر القائمة
        self.create_menu_button()
        
        # إضافة زر الرجوع
        self.create_back_button()
        
    def create_menu_button(self):
        """إنشاء زر القائمة"""
        try:
            from PyQt5.QtWidgets import QPushButton
            
            menu_btn = QPushButton("☰")
            menu_btn.setObjectName("mobile-menu-btn")
            menu_btn.setFixedSize(44, 44)
            menu_btn.clicked.connect(self.toggle_sidebar)
            
            # إضافة الزر للواجهة
            if hasattr(self.main_window, 'main_content'):
                # يمكن إضافة الزر لشريط علوي
                pass
                
        except Exception as e:
            print(f"خطأ في إنشاء زر القائمة: {e}")
    
    def create_back_button(self):
        """إنشاء زر الرجوع"""
        try:
            from PyQt5.QtWidgets import QPushButton
            
            back_btn = QPushButton("←")
            back_btn.setObjectName("mobile-back-btn")
            back_btn.setFixedSize(44, 44)
            back_btn.clicked.connect(self.go_back)
            
            # إخفاء الزر إذا لم يكن هناك تاريخ
            back_btn.setVisible(len(self.navigation_history) > 1)
            
        except Exception as e:
            print(f"خطأ في إنشاء زر الرجوع: {e}")
    
    def toggle_sidebar(self):
        """تبديل إظهار/إخفاء الشريط الجانبي"""
        if hasattr(self.main_window, 'sidebar'):
            self.sidebar_visible = not self.sidebar_visible
            
            if self.sidebar_visible:
                self.show_sidebar_overlay()
            else:
                self.hide_sidebar_overlay()
    
    def show_sidebar_overlay(self):
        """إظهار الشريط الجانبي كطبقة علوية"""
        try:
            sidebar = self.main_window.sidebar
            
            # تحديد موقع الشريط الجانبي
            sidebar.setParent(self.main_window)
            sidebar.raise_()
            sidebar.setVisible(True)
            
            # تحريك الشريط من اليمين
            self.animate_sidebar_in()
            
            # إضافة طبقة شفافة للخلفية
            self.create_overlay()
            
        except Exception as e:
            print(f"خطأ في إظهار الشريط الجانبي: {e}")
    
    def hide_sidebar_overlay(self):
        """إخفاء الشريط الجانبي"""
        try:
            # تحريك الشريط خارج الشاشة
            self.animate_sidebar_out()
            
            # إزالة الطبقة الشفافة
            self.remove_overlay()
            
        except Exception as e:
            print(f"خطأ في إخفاء الشريط الجانبي: {e}")
    
    def animate_sidebar_in(self):
        """تحريك الشريط الجانبي للداخل"""
        try:
            sidebar = self.main_window.sidebar
            
            # الموقع النهائي
            end_rect = QRect(0, 0, 250, self.main_window.height())
            
            # إنشاء الرسوم المتحركة
            self.sidebar_animation = QPropertyAnimation(sidebar, b"geometry")
            self.sidebar_animation.setDuration(300)
            self.sidebar_animation.setEasingCurve(QEasingCurve.OutCubic)
            
            # الموقع البدائي (خارج الشاشة)
            start_rect = QRect(-250, 0, 250, self.main_window.height())
            
            self.sidebar_animation.setStartValue(start_rect)
            self.sidebar_animation.setEndValue(end_rect)
            self.sidebar_animation.start()
            
        except Exception as e:
            print(f"خطأ في تحريك الشريط: {e}")
    
    def animate_sidebar_out(self):
        """تحريك الشريط الجانبي للخارج"""
        try:
            sidebar = self.main_window.sidebar
            
            # الموقع النهائي (خارج الشاشة)
            end_rect = QRect(-250, 0, 250, self.main_window.height())
            
            # إنشاء الرسوم المتحركة
            self.sidebar_animation = QPropertyAnimation(sidebar, b"geometry")
            self.sidebar_animation.setDuration(300)
            self.sidebar_animation.setEasingCurve(QEasingCurve.InCubic)
            
            # الموقع البدائي
            start_rect = sidebar.geometry()
            
            self.sidebar_animation.setStartValue(start_rect)
            self.sidebar_animation.setEndValue(end_rect)
            self.sidebar_animation.finished.connect(lambda: sidebar.setVisible(False))
            self.sidebar_animation.start()
            
        except Exception as e:
            print(f"خطأ في تحريك الشريط: {e}")
    
    def create_overlay(self):
        """إنشاء طبقة شفافة للخلفية"""
        try:
            from PyQt5.QtWidgets import QFrame
            
            self.overlay = QFrame(self.main_window)
            self.overlay.setObjectName("sidebar-overlay")
            self.overlay.setStyleSheet("""
                QFrame#sidebar-overlay {
                    background-color: rgba(0, 0, 0, 0.5);
                }
            """)
            
            # تغطية كامل النافذة
            self.overlay.setGeometry(0, 0, self.main_window.width(), self.main_window.height())
            self.overlay.show()
            
            # إغلاق الشريط عند النقر على الطبقة
            self.overlay.mousePressEvent = lambda e: self.hide_sidebar_overlay()
            
        except Exception as e:
            print(f"خطأ في إنشاء الطبقة الشفافة: {e}")
    
    def remove_overlay(self):
        """إزالة الطبقة الشفافة"""
        try:
            if hasattr(self, 'overlay'):
                self.overlay.deleteLater()
                delattr(self, 'overlay')
        except Exception as e:
            print(f"خطأ في إزالة الطبقة الشفافة: {e}")
    
    def navigate_to_page(self, page_name):
        """التنقل لصفحة معينة"""
        # إضافة الصفحة الحالية للتاريخ
        if self.current_page:
            self.navigation_history.append(self.current_page)
            
        self.current_page = page_name
        self.navigation_changed.emit(page_name)
        
        # إخفاء الشريط الجانبي بعد التنقل
        if self.sidebar_visible:
            self.hide_sidebar_overlay()
    
    def go_back(self):
        """الرجوع للصفحة السابقة"""
        if len(self.navigation_history) > 0:
            previous_page = self.navigation_history.pop()
            self.current_page = previous_page
            self.navigation_changed.emit(previous_page)

class TouchFeedbackManager(QObject):
    """مدير ردود الفعل اللمسية"""
    
    def __init__(self):
        super().__init__()
        self.haptic_enabled = True
        self.visual_feedback_enabled = True
        self.audio_feedback_enabled = False
        
    def provide_touch_feedback(self, widget, feedback_type="tap"):
        """توفير رد فعل لمسي"""
        if self.visual_feedback_enabled:
            self.provide_visual_feedback(widget, feedback_type)
            
        if self.haptic_enabled:
            self.provide_haptic_feedback(feedback_type)
            
        if self.audio_feedback_enabled:
            self.provide_audio_feedback(feedback_type)
    
    def provide_visual_feedback(self, widget, feedback_type):
        """توفير رد فعل بصري"""
        try:
            # تأثير الضغط
            if feedback_type == "tap":
                self.create_press_effect(widget)
            elif feedback_type == "success":
                self.create_success_effect(widget)
            elif feedback_type == "error":
                self.create_error_effect(widget)
                
        except Exception as e:
            print(f"خطأ في رد الفعل البصري: {e}")
    
    def create_press_effect(self, widget):
        """إنشاء تأثير الضغط"""
        try:
            # تصغير مؤقت للويدجت
            original_size = widget.size()
            
            # رسوم متحركة للتصغير
            shrink_animation = QPropertyAnimation(widget, b"size")
            shrink_animation.setDuration(100)
            shrink_animation.setStartValue(original_size)
            shrink_animation.setEndValue(original_size * 0.95)
            
            # رسوم متحركة للإرجاع
            expand_animation = QPropertyAnimation(widget, b"size")
            expand_animation.setDuration(100)
            expand_animation.setStartValue(original_size * 0.95)
            expand_animation.setEndValue(original_size)
            
            # ربط الرسوم المتحركة
            shrink_animation.finished.connect(expand_animation.start)
            shrink_animation.start()
            
        except Exception as e:
            print(f"خطأ في تأثير الضغط: {e}")
    
    def create_success_effect(self, widget):
        """إنشاء تأثير النجاح"""
        try:
            # تغيير لون الخلفية مؤقتاً
            original_style = widget.styleSheet()
            
            widget.setStyleSheet(original_style + """
                background-color: #27ae60;
                border: 2px solid #229954;
            """)
            
            # إرجاع اللون الأصلي بعد فترة
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(500, lambda: widget.setStyleSheet(original_style))
            
        except Exception as e:
            print(f"خطأ في تأثير النجاح: {e}")
    
    def create_error_effect(self, widget):
        """إنشاء تأثير الخطأ"""
        try:
            # اهتزاز الويدجت
            original_pos = widget.pos()
            
            # رسوم متحركة للاهتزاز
            shake_animation = QPropertyAnimation(widget, b"pos")
            shake_animation.setDuration(300)
            shake_animation.setKeyValueAt(0, original_pos)
            shake_animation.setKeyValueAt(0.25, original_pos + QRect(5, 0, 0, 0).topLeft())
            shake_animation.setKeyValueAt(0.5, original_pos + QRect(-5, 0, 0, 0).topLeft())
            shake_animation.setKeyValueAt(0.75, original_pos + QRect(5, 0, 0, 0).topLeft())
            shake_animation.setKeyValueAt(1, original_pos)
            
            shake_animation.start()
            
        except Exception as e:
            print(f"خطأ في تأثير الخطأ: {e}")
    
    def provide_haptic_feedback(self, feedback_type):
        """توفير رد فعل اهتزازي"""
        # ملاحظة: PyQt لا يدعم الاهتزاز مباشرة
        # يمكن استخدام مكتبات خارجية أو API النظام
        pass
    
    def provide_audio_feedback(self, feedback_type):
        """توفير رد فعل صوتي"""
        try:
            if feedback_type == "tap":
                # صوت نقرة خفيف
                pass
            elif feedback_type == "success":
                # صوت نجاح
                pass
            elif feedback_type == "error":
                # صوت خطأ
                pass
        except Exception as e:
            print(f"خطأ في رد الفعل الصوتي: {e}")

class SwipeGestureHandler(QObject):
    """معالج إيماءات السحب"""

    swipe_detected = pyqtSignal(str)  # اتجاه السحب

    def __init__(self, widget):
        super().__init__()
        self.widget = widget
        self.start_pos = None
        self.min_swipe_distance = 50

    def handle_mouse_press(self, event):
        """معالجة بداية السحب"""
        if event.button() == Qt.LeftButton:
            self.start_pos = event.pos()

    def handle_mouse_release(self, event):
        """معالجة نهاية السحب"""
        if self.start_pos and event.button() == Qt.LeftButton:
            end_pos = event.pos()
            self.detect_swipe_direction(self.start_pos, end_pos)
            self.start_pos = None

    def detect_swipe_direction(self, start_pos, end_pos):
        """كشف اتجاه السحب"""
        dx = end_pos.x() - start_pos.x()
        dy = end_pos.y() - start_pos.y()

        # فحص المسافة الدنيا
        if abs(dx) < self.min_swipe_distance and abs(dy) < self.min_swipe_distance:
            return

        # تحديد الاتجاه الرئيسي
        if abs(dx) > abs(dy):
            # سحب أفقي
            if dx > 0:
                self.swipe_detected.emit("right")
            else:
                self.swipe_detected.emit("left")
        else:
            # سحب عمودي
            if dy > 0:
                self.swipe_detected.emit("down")
            else:
                self.swipe_detected.emit("up")

def apply_touch_optimizations_to_app(main_window):
    """تطبيق تحسينات اللمس على التطبيق"""
    try:
        # إنشاء محسن اللمس
        touch_optimizer = TouchOptimizer()

        # إنشاء مدير التنقل للهاتف
        nav_manager = MobileNavigationManager(main_window)

        # إنشاء مدير ردود الفعل
        feedback_manager = TouchFeedbackManager()

        # تطبيق التحسينات على النافذة الرئيسية
        touch_optimizer.enable_touch_for_widget(main_window)

        # تطبيق التحسينات على جميع الصفحات
        pages = [
            main_window.dashboard_page,
            main_window.budget_page,
            main_window.goals_page,
            main_window.add_expense_page,
            main_window.manage_expenses_page,
            main_window.notifications_page,
            main_window.ai_insights_page
        ]

        for page in pages:
            if page:
                touch_optimizer.enable_touch_for_widget(page)

        # إعداد التنقل للهاتف
        nav_manager.setup_mobile_navigation()

        # ربط الإشارات
        touch_optimizer.gesture_detected.connect(
            lambda gesture_type, data: feedback_manager.provide_touch_feedback(
                data['widget'], gesture_type
            )
        )

        return {
            'touch_optimizer': touch_optimizer,
            'nav_manager': nav_manager,
            'feedback_manager': feedback_manager
        }

    except Exception as e:
        print(f"خطأ في تطبيق تحسينات اللمس: {e}")
        return None
