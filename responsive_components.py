#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات UI متجاوبة متقدمة
تحتوي على أزرار، بطاقات، جداول، ونماذج متجاوبة
"""

from PyQt5.QtWidgets import (QPushButton, QFrame, QLabel, QVBoxLayout, QHBoxLayout,
                             QTableWidget, QLineEdit, QComboBox, QTextEdit, QProgressBar,
                             QCheckBox, QRadioButton, QSpinBox, QDoubleSpinBox, QSlider,
                             QGroupBox, QScrollArea, QWidget, QSizePolicy)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QBrush, QPen
from responsive_utils import ResponsiveUtils, ResponsiveBreakpoints

class ResponsiveButton(QPushButton):
    """زر متجاوب متقدم"""
    
    def __init__(self, text="", button_type="primary", size="md", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.size_category = size
        self.current_responsive_size = "md"
        self.setup_button()
        
    def setup_button(self):
        """إعداد الزر"""
        # تطبيق النوع
        self.setProperty("class", f"btn-{self.button_type}")
        
        # تطبيق الحجم
        self.apply_size_settings()
        
        # تفعيل التأثيرات
        self.setGraphicsEffect(None)
        
    def apply_size_settings(self):
        """تطبيق إعدادات الحجم"""
        size_configs = {
            'xs': {'padding': (8, 12), 'font_size': 12, 'min_height': 36},
            'sm': {'padding': (10, 16), 'font_size': 13, 'min_height': 40},
            'md': {'padding': (12, 20), 'font_size': 14, 'min_height': 44},
            'lg': {'padding': (14, 24), 'font_size': 15, 'min_height': 48},
            'xl': {'padding': (16, 28), 'font_size': 16, 'min_height': 52}
        }
        
        config = size_configs.get(self.size_category, size_configs['md'])
        
        # تطبيق الحد الأدنى للارتفاع
        self.setMinimumHeight(config['min_height'])
        
        # تطبيق حجم الخط
        font = self.font()
        font.setPointSize(config['font_size'])
        self.setFont(font)
        
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الحجم
            if size_category in ['xs', 'sm']:
                self.setMinimumWidth(120)
                self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
            else:
                self.setMinimumWidth(80)
                self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
                
            # إعادة تطبيق الأنماط
            self.style().unpolish(self)
            self.style().polish(self)

class ResponsiveCard(QFrame):
    """بطاقة متجاوبة متقدمة"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title="", content="", card_type="default", parent=None):
        super().__init__(parent)
        self.card_title = title
        self.card_content = content
        self.card_type = card_type
        self.current_responsive_size = "md"
        self.setup_card()
        
    def setup_card(self):
        """إعداد البطاقة"""
        self.setObjectName("card")
        self.setProperty("card-type", self.card_type)
        
        # إعداد التخطيط
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 20, 20, 20)
        self.layout.setSpacing(15)
        
        # إضافة العنوان
        if self.card_title:
            self.title_label = QLabel(self.card_title)
            self.title_label.setObjectName("card_title")
            self.layout.addWidget(self.title_label)
        
        # إضافة المحتوى
        if self.card_content:
            self.content_label = QLabel(self.card_content)
            self.content_label.setWordWrap(True)
            self.layout.addWidget(self.content_label)
            
        # تفعيل التفاعل
        self.setMouseTracking(True)
        
    def add_widget(self, widget):
        """إضافة ويدجت للبطاقة"""
        self.layout.addWidget(widget)
        
    def set_title(self, title):
        """تعيين العنوان"""
        if hasattr(self, 'title_label'):
            self.title_label.setText(title)
        else:
            self.title_label = QLabel(title)
            self.title_label.setObjectName("card_title")
            self.layout.insertWidget(0, self.title_label)
            
    def set_content(self, content):
        """تعيين المحتوى"""
        if hasattr(self, 'content_label'):
            self.content_label.setText(content)
        else:
            self.content_label = QLabel(content)
            self.content_label.setWordWrap(True)
            self.layout.addWidget(self.content_label)
            
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الحجم
            ResponsiveUtils.apply_responsive_margins(self, 20, size_category)
            ResponsiveUtils.apply_responsive_spacing(self.layout, 15, size_category)
            
            # تطبيق أحجام الخطوط
            if hasattr(self, 'title_label'):
                ResponsiveUtils.apply_responsive_font(self.title_label, 16, size_category)
            if hasattr(self, 'content_label'):
                ResponsiveUtils.apply_responsive_font(self.content_label, 14, size_category)
                
            # إعادة تطبيق الأنماط
            ResponsiveUtils.apply_responsive_card_style(self, size_category)
            
    def mousePressEvent(self, event):
        """معالج النقر"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class ResponsiveTable(QTableWidget):
    """جدول متجاوب متقدم"""
    
    def __init__(self, rows=0, columns=0, parent=None):
        super().__init__(rows, columns, parent)
        self.current_responsive_size = "md"
        self.mobile_mode = False
        self.setup_table()
        
    def setup_table(self):
        """إعداد الجدول"""
        # إعدادات أساسية
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # تحسين الأداء
        self.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        self.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
        
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الجدول
            ResponsiveUtils.apply_responsive_table_style(self, size_category)
            
            # تحديد وضع الهاتف
            self.mobile_mode = size_category in ['xs', 'sm']
            
            if self.mobile_mode:
                self.apply_mobile_table_style()
            else:
                self.apply_desktop_table_style()
                
    def apply_mobile_table_style(self):
        """تطبيق أنماط الهاتف"""
        # تقليل حجم الخط
        font = self.font()
        font.setPointSize(12)
        self.setFont(font)
        
        # تقليل ارتفاع الصفوف
        self.verticalHeader().setDefaultSectionSize(35)
        
        # إخفاء الرأس العمودي
        self.verticalHeader().setVisible(False)
        
        # تحسين عرض الأعمدة
        self.resizeColumnsToContents()
        
    def apply_desktop_table_style(self):
        """تطبيق أنماط سطح المكتب"""
        # حجم خط عادي
        font = self.font()
        font.setPointSize(14)
        self.setFont(font)
        
        # ارتفاع صفوف عادي
        self.verticalHeader().setDefaultSectionSize(45)
        
        # إظهار الرأس العمودي
        self.verticalHeader().setVisible(True)

class ResponsiveInput(QLineEdit):
    """حقل إدخال متجاوب"""
    
    def __init__(self, placeholder="", input_type="text", parent=None):
        super().__init__(parent)
        self.input_type = input_type
        self.current_responsive_size = "md"
        self.setup_input(placeholder)
        
    def setup_input(self, placeholder):
        """إعداد حقل الإدخال"""
        self.setPlaceholderText(placeholder)
        
        # تطبيق نوع الإدخال
        if self.input_type == "email":
            self.setInputMask("")
        elif self.input_type == "phone":
            self.setInputMask("************")
        elif self.input_type == "number":
            self.setInputMask("999999999")
            
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الحجم
            if size_category in ['xs', 'sm']:
                self.setMinimumHeight(44)  # للمس السهل
                ResponsiveUtils.apply_responsive_font(self, 16, size_category)  # منع التكبير في iOS
            else:
                self.setMinimumHeight(36)
                ResponsiveUtils.apply_responsive_font(self, 14, size_category)

class ResponsiveComboBox(QComboBox):
    """قائمة منسدلة متجاوبة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_responsive_size = "md"
        self.setup_combo()
        
    def setup_combo(self):
        """إعداد القائمة المنسدلة"""
        # إعدادات أساسية
        self.setEditable(False)
        
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الحجم
            if size_category in ['xs', 'sm']:
                self.setMinimumHeight(44)
                ResponsiveUtils.apply_responsive_font(self, 16, size_category)
            else:
                self.setMinimumHeight(36)
                ResponsiveUtils.apply_responsive_font(self, 14, size_category)

class ResponsiveProgressBar(QProgressBar):
    """شريط تقدم متجاوب"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_responsive_size = "md"
        self.setup_progress()
        
    def setup_progress(self):
        """إعداد شريط التقدم"""
        self.setTextVisible(True)
        self.setAlignment(Qt.AlignCenter)
        
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات الحجم
            height_configs = {
                'xs': 20,
                'sm': 24,
                'md': 28,
                'lg': 32,
                'xl': 36
            }
            
            height = height_configs.get(size_category, 28)
            self.setFixedHeight(height)
            
            # تطبيق حجم الخط
            ResponsiveUtils.apply_responsive_font(self, 12, size_category)

class ResponsiveForm(QFrame):
    """نموذج متجاوب متقدم"""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.form_title = title
        self.current_responsive_size = "md"
        self.fields = []
        self.setup_form()
        
    def setup_form(self):
        """إعداد النموذج"""
        self.setObjectName("responsive-form")
        
        # إعداد التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(15)
        
        # إضافة العنوان
        if self.form_title:
            title_label = QLabel(self.form_title)
            title_label.setObjectName("form-title")
            self.main_layout.addWidget(title_label)
            
        # إنشاء منطقة التمرير
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.form_layout = QVBoxLayout(self.content_widget)
        
        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area)
        
    def add_field(self, label_text, widget, required=False):
        """إضافة حقل للنموذج"""
        field_container = QFrame()
        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(5)
        
        # إضافة التسمية
        label = QLabel(label_text)
        if required:
            label.setText(f"{label_text} *")
            label.setStyleSheet("color: #e74c3c;")
        field_layout.addWidget(label)
        
        # إضافة الويدجت
        field_layout.addWidget(widget)
        
        # حفظ مرجع الحقل
        self.fields.append({
            'label': label,
            'widget': widget,
            'container': field_container,
            'required': required
        })
        
        self.form_layout.addWidget(field_container)
        
    def update_responsive_size(self, size_category):
        """تحديث الحجم المتجاوب"""
        if size_category != self.current_responsive_size:
            self.current_responsive_size = size_category
            
            # تطبيق تحسينات النموذج
            ResponsiveUtils.apply_responsive_margins(self, 20, size_category)
            ResponsiveUtils.apply_responsive_spacing(self.form_layout, 15, size_category)
            
            # تحديث جميع الحقول
            for field in self.fields:
                if hasattr(field['widget'], 'update_responsive_size'):
                    field['widget'].update_responsive_size(size_category)
                    
                # تطبيق حجم خط التسمية
                ResponsiveUtils.apply_responsive_font(field['label'], 14, size_category)
                
    def validate_form(self):
        """التحقق من صحة النموذج"""
        errors = []
        
        for field in self.fields:
            if field['required']:
                widget = field['widget']
                
                # فحص حقول النص
                if isinstance(widget, (QLineEdit, QTextEdit)):
                    if not widget.text().strip():
                        errors.append(f"حقل '{field['label'].text()}' مطلوب")
                        
                # فحص القوائم المنسدلة
                elif isinstance(widget, QComboBox):
                    if widget.currentIndex() == -1:
                        errors.append(f"يرجى اختيار قيمة من '{field['label'].text()}'")
                        
        return errors
