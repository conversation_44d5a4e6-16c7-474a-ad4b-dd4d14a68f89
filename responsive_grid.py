#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الشبكة المتجاوب المتقدم
يوفر تخطيطات مرنة وقابلة للتكيف مع جميع أحجام الشاشات
"""

from PyQt5.QtWidgets import QWidget, QGridLayout, QVBoxLayout, QHBoxLayout, QFrame
from PyQt5.QtWidgets import QSizePolicy, QSpacerItem
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QResizeEvent

class ResponsiveGridWidget(QWidget):
    """ويدجت الشبكة المتجاوبة"""
    
    layout_changed = pyqtSignal(str)  # إشارة تغيير التخطيط
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_columns = 3
        self.items = []
        self.breakpoints = {
            'xs': 480,
            'sm': 768, 
            'md': 1024,
            'lg': 1440,
            'xl': 1920
        }
        
        # إعداد التخطيط
        self.setup_layout()
        
    def setup_layout(self):
        """إعداد التخطيط الأساسي"""
        self.grid_layout = QGridLayout(self)
        self.grid_layout.setSpacing(15)
        self.grid_layout.setContentsMargins(10, 10, 10, 10)
        
    def add_item(self, widget, stretch=1):
        """إضافة عنصر للشبكة"""
        self.items.append({
            'widget': widget,
            'stretch': stretch
        })
        self.update_grid_layout()
        
    def remove_item(self, widget):
        """إزالة عنصر من الشبكة"""
        self.items = [item for item in self.items if item['widget'] != widget]
        widget.setParent(None)
        self.update_grid_layout()
        
    def clear_items(self):
        """مسح جميع العناصر"""
        for item in self.items:
            item['widget'].setParent(None)
        self.items.clear()
        
    def set_columns_for_size(self, size_category, columns):
        """تعيين عدد الأعمدة لحجم معين"""
        if size_category in ['xs', 'sm', 'md', 'lg', 'xl']:
            setattr(self, f'{size_category}_columns', columns)
            
    def get_current_size_category(self):
        """الحصول على فئة الحجم الحالية"""
        width = self.width()
        
        if width <= self.breakpoints['xs']:
            return 'xs'
        elif width <= self.breakpoints['sm']:
            return 'sm'
        elif width <= self.breakpoints['md']:
            return 'md'
        elif width <= self.breakpoints['lg']:
            return 'lg'
        else:
            return 'xl'
            
    def get_columns_for_size(self, size_category):
        """الحصول على عدد الأعمدة لحجم معين"""
        # القيم الافتراضية
        default_columns = {
            'xs': 1,
            'sm': 2,
            'md': 3,
            'lg': 3,
            'xl': 4
        }
        
        # البحث عن قيمة مخصصة أولاً
        custom_attr = f'{size_category}_columns'
        if hasattr(self, custom_attr):
            return getattr(self, custom_attr)
            
        return default_columns.get(size_category, 3)
        
    def update_grid_layout(self):
        """تحديث تخطيط الشبكة"""
        size_category = self.get_current_size_category()
        new_columns = self.get_columns_for_size(size_category)
        
        if new_columns != self.current_columns:
            self.current_columns = new_columns
            self.rearrange_items()
            self.layout_changed.emit(size_category)
            
    def rearrange_items(self):
        """إعادة ترتيب العناصر في الشبكة"""
        # مسح التخطيط الحالي
        for i in reversed(range(self.grid_layout.count())):
            item = self.grid_layout.itemAt(i)
            if item:
                self.grid_layout.removeItem(item)
                
        # إعادة ترتيب العناصر
        for i, item_data in enumerate(self.items):
            widget = item_data['widget']
            row = i // self.current_columns
            col = i % self.current_columns
            
            self.grid_layout.addWidget(widget, row, col)
            
        # إضافة مساحة مرنة في النهاية
        self.add_stretch_to_grid()
        
    def add_stretch_to_grid(self):
        """إضافة مساحة مرنة للشبكة"""
        # حساب الصف الأخير
        if self.items:
            last_row = (len(self.items) - 1) // self.current_columns + 1
            
            # إضافة مساحة مرنة عمودية
            spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
            self.grid_layout.addItem(spacer, last_row, 0, 1, self.current_columns)
            
    def resizeEvent(self, event: QResizeEvent):
        """معالج تغيير الحجم"""
        super().resizeEvent(event)
        self.update_grid_layout()

class ResponsiveContainer(QFrame):
    """حاوية متجاوبة مع تخطيط تلقائي"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_container()
        
    def setup_container(self):
        """إعداد الحاوية"""
        self.setObjectName("responsive-container")
        
        # تعيين سياسة الحجم
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # إعداد التخطيط الأساسي
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
    def add_responsive_section(self, widget, mobile_full_width=True):
        """إضافة قسم متجاوب"""
        section = ResponsiveSection(widget, mobile_full_width)
        self.main_layout.addWidget(section)
        return section
        
    def add_responsive_grid(self, columns_config=None):
        """إضافة شبكة متجاوبة"""
        if columns_config is None:
            columns_config = {'xs': 1, 'sm': 2, 'md': 3, 'lg': 3, 'xl': 4}
            
        grid = ResponsiveGridWidget()
        
        # تطبيق إعدادات الأعمدة
        for size, cols in columns_config.items():
            grid.set_columns_for_size(size, cols)
            
        self.main_layout.addWidget(grid)
        return grid

class ResponsiveSection(QFrame):
    """قسم متجاوب يمكنه التكيف مع أحجام الشاشة"""
    
    def __init__(self, content_widget, mobile_full_width=True, parent=None):
        super().__init__(parent)
        self.content_widget = content_widget
        self.mobile_full_width = mobile_full_width
        self.setup_section()
        
    def setup_section(self):
        """إعداد القسم"""
        self.setObjectName("responsive-section")
        
        # إعداد التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.content_widget)
        
    def resizeEvent(self, event: QResizeEvent):
        """معالج تغيير الحجم"""
        super().resizeEvent(event)
        
        width = self.width()
        
        # تطبيق تحسينات الهاتف
        if width <= 768 and self.mobile_full_width:
            self.content_widget.setMaximumWidth(16777215)  # إزالة الحد الأقصى
        else:
            # تطبيق عرض محدود للشاشات الكبيرة
            max_width = min(1200, width - 40)
            self.content_widget.setMaximumWidth(max_width)

class ResponsiveRow(QFrame):
    """صف متجاوب يحتوي على أعمدة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.columns = []
        self.setup_row()
        
    def setup_row(self):
        """إعداد الصف"""
        self.setObjectName("responsive-row")
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(15)
        
    def add_column(self, widget, xs=12, sm=6, md=4, lg=3, xl=3):
        """إضافة عمود متجاوب"""
        column = ResponsiveColumn(widget, xs, sm, md, lg, xl)
        self.columns.append(column)
        self.layout.addWidget(column)
        return column
        
    def resizeEvent(self, event: QResizeEvent):
        """معالج تغيير الحجم"""
        super().resizeEvent(event)
        self.update_columns_layout()
        
    def update_columns_layout(self):
        """تحديث تخطيط الأعمدة"""
        width = self.width()
        
        # تحديد فئة الحجم
        if width <= 480:
            size_category = 'xs'
        elif width <= 768:
            size_category = 'sm'
        elif width <= 1024:
            size_category = 'md'
        elif width <= 1440:
            size_category = 'lg'
        else:
            size_category = 'xl'
            
        # تحديث جميع الأعمدة
        for column in self.columns:
            column.update_for_size(size_category)

class ResponsiveColumn(QFrame):
    """عمود متجاوب"""
    
    def __init__(self, widget, xs=12, sm=6, md=4, lg=3, xl=3, parent=None):
        super().__init__(parent)
        self.content_widget = widget
        self.sizes = {'xs': xs, 'sm': sm, 'md': md, 'lg': lg, 'xl': xl}
        self.setup_column()
        
    def setup_column(self):
        """إعداد العمود"""
        self.setObjectName("responsive-column")
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.addWidget(self.content_widget)
        
    def update_for_size(self, size_category):
        """تحديث العمود لحجم معين"""
        column_size = self.sizes.get(size_category, 12)
        
        # حساب العرض كنسبة مئوية
        width_percentage = (column_size / 12.0) * 100
        
        # تطبيق العرض (تقريبي في PyQt)
        if column_size == 12:
            # عرض كامل
            self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        else:
            # عرض جزئي
            self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

class FlexLayout(QHBoxLayout):
    """تخطيط مرن يشبه CSS Flexbox"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.flex_items = []
        
    def addWidget(self, widget, flex=1, align=Qt.AlignCenter):
        """إضافة ويدجت مع خصائص مرونة"""
        super().addWidget(widget, flex)
        
        self.flex_items.append({
            'widget': widget,
            'flex': flex,
            'align': align
        })
        
        # تطبيق المحاذاة
        widget.setAlignment(align)
        
    def set_direction(self, direction='row'):
        """تعيين اتجاه التخطيط"""
        if direction == 'column':
            # تحويل إلى تخطيط عمودي
            new_layout = QVBoxLayout()
            
            # نقل جميع العناصر
            while self.count():
                item = self.takeAt(0)
                if item.widget():
                    new_layout.addWidget(item.widget())
                    
            # استبدال التخطيط
            if self.parent():
                self.parent().setLayout(new_layout)
                
    def set_justify_content(self, justify='center'):
        """تعيين توزيع المحتوى"""
        if justify == 'space-between':
            self.addStretch(0)  # في البداية
            for i in range(len(self.flex_items) - 1):
                self.insertStretch(i * 2 + 1, 1)  # بين العناصر
            self.addStretch(0)  # في النهاية
        elif justify == 'space-around':
            for i in range(len(self.flex_items) + 1):
                self.insertStretch(i * 2, 1)
        elif justify == 'center':
            self.addStretch(1)
            self.addStretch(1)
        elif justify == 'flex-start':
            self.addStretch(1)
        elif justify == 'flex-end':
            self.insertStretch(0, 1)
