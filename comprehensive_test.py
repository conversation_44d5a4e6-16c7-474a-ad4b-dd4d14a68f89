#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام المتجاوب
يختبر جميع الميزات والمكونات المطبقة
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtTest import QTest

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ResponsiveTestSuite(QThread):
    """مجموعة اختبارات التجاوب"""
    
    test_completed = pyqtSignal(str, bool, str)  # اسم الاختبار، النتيجة، التفاصيل
    all_tests_completed = pyqtSignal(dict)  # نتائج جميع الاختبارات
    
    def __init__(self):
        super().__init__()
        self.test_results = {}
        
    def run(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء الاختبارات الشاملة للنظام المتجاوب...")
        print("=" * 60)
        
        # اختبار الملفات الأساسية
        self.test_core_files()
        
        # اختبار استيراد المكتبات
        self.test_imports()
        
        # اختبار نظام الثيمات
        self.test_theme_system()
        
        # اختبار المكونات المتجاوبة
        self.test_responsive_components()
        
        # اختبار نقاط التوقف
        self.test_breakpoints()
        
        # اختبار التكامل
        self.test_integration()
        
        print("=" * 60)
        self.print_test_summary()
        
        self.all_tests_completed.emit(self.test_results)
        
    def test_core_files(self):
        """اختبار وجود الملفات الأساسية"""
        test_name = "الملفات الأساسية"
        
        required_files = [
            "styles.qss",
            "responsive_manager.py",
            "theme_manager.py", 
            "responsive_components.py",
            "responsive_grid.py",
            "layout_manager.py",
            "responsive_utils.py",
            "touch_optimization.py"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
                
        if missing_files:
            result = False
            details = f"ملفات مفقودة: {', '.join(missing_files)}"
        else:
            result = True
            details = "جميع الملفات الأساسية موجودة"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def test_imports(self):
        """اختبار استيراد المكتبات"""
        test_name = "استيراد المكتبات"
        
        try:
            # اختبار استيراد المكونات الأساسية
            from responsive_manager import ResponsiveManager
            from theme_manager import ThemeManager, ResponsiveThemeManager
            from responsive_components import ResponsiveButton, ResponsiveCard
            from responsive_grid import ResponsiveGridWidget
            from responsive_utils import ResponsiveUtils
            from touch_optimization import TouchOptimizer
            
            result = True
            details = "تم استيراد جميع المكتبات بنجاح"
            
        except ImportError as e:
            result = False
            details = f"خطأ في الاستيراد: {str(e)}"
        except Exception as e:
            result = False
            details = f"خطأ غير متوقع: {str(e)}"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def test_theme_system(self):
        """اختبار نظام الثيمات"""
        test_name = "نظام الثيمات"
        
        try:
            from theme_manager import ResponsiveThemeManager
            
            theme_manager = ResponsiveThemeManager()
            
            # اختبار الثيمات المتاحة
            themes = theme_manager.get_available_themes()
            expected_themes = ["light", "dark", "blue", "green"]
            
            available_theme_names = [theme[0] for theme in themes]
            missing_themes = [t for t in expected_themes if t not in available_theme_names]
            
            if missing_themes:
                result = False
                details = f"ثيمات مفقودة: {', '.join(missing_themes)}"
            else:
                # اختبار تغيير الثيم
                theme_manager.set_theme("dark")
                current_theme = theme_manager.get_current_theme()
                
                if current_theme == "dark":
                    result = True
                    details = f"نظام الثيمات يعمل بنجاح. الثيمات المتاحة: {len(themes)}"
                else:
                    result = False
                    details = "فشل في تغيير الثيم"
                    
        except Exception as e:
            result = False
            details = f"خطأ في نظام الثيمات: {str(e)}"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def test_responsive_components(self):
        """اختبار المكونات المتجاوبة"""
        test_name = "المكونات المتجاوبة"
        
        try:
            from responsive_components import (ResponsiveButton, ResponsiveCard, 
                                             ResponsiveTable, ResponsiveInput)
            
            # اختبار إنشاء المكونات
            button = ResponsiveButton("اختبار", "primary", "md")
            card = ResponsiveCard("عنوان", "محتوى", "default")
            table = ResponsiveTable(3, 3)
            input_field = ResponsiveInput("اختبار", "text")
            
            # اختبار تحديث الحجم
            button.update_responsive_size("sm")
            card.update_responsive_size("lg")
            table.update_responsive_size("xs")
            input_field.update_responsive_size("xl")
            
            result = True
            details = "جميع المكونات المتجاوبة تعمل بنجاح"
            
        except Exception as e:
            result = False
            details = f"خطأ في المكونات المتجاوبة: {str(e)}"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def test_breakpoints(self):
        """اختبار نقاط التوقف"""
        test_name = "نقاط التوقف"
        
        try:
            from responsive_utils import ResponsiveBreakpoints
            
            # اختبار تصنيف الأحجام
            test_cases = [
                (320, 'xs'),
                (480, 'xs'),
                (600, 'sm'),
                (768, 'sm'),
                (900, 'md'),
                (1024, 'md'),
                (1200, 'lg'),
                (1440, 'lg'),
                (1920, 'xl')
            ]
            
            failed_cases = []
            for width, expected in test_cases:
                actual = ResponsiveBreakpoints.get_size_category(width)
                if actual != expected:
                    failed_cases.append(f"{width}px -> {actual} (متوقع: {expected})")
                    
            if failed_cases:
                result = False
                details = f"فشل في: {', '.join(failed_cases)}"
            else:
                result = True
                details = f"جميع نقاط التوقف تعمل بنجاح ({len(test_cases)} اختبار)"
                
        except Exception as e:
            result = False
            details = f"خطأ في نقاط التوقف: {str(e)}"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def test_integration(self):
        """اختبار التكامل"""
        test_name = "التكامل العام"
        
        try:
            # اختبار تحديث الملف الرئيسي
            if os.path.exists("main_window.py"):
                with open("main_window.py", "r", encoding="utf-8") as f:
                    content = f.read()
                    
                required_integrations = [
                    "ResponsiveManager",
                    "ThemeManager", 
                    "setup_complete_responsive_system",
                    "update_responsive_layout"
                ]
                
                missing_integrations = []
                for integration in required_integrations:
                    if integration not in content:
                        missing_integrations.append(integration)
                        
                if missing_integrations:
                    result = False
                    details = f"تكاملات مفقودة: {', '.join(missing_integrations)}"
                else:
                    result = True
                    details = "التكامل مكتمل في الملف الرئيسي"
            else:
                result = False
                details = "الملف الرئيسي غير موجود"
                
        except Exception as e:
            result = False
            details = f"خطأ في اختبار التكامل: {str(e)}"
            
        self.test_results[test_name] = {"result": result, "details": details}
        self.test_completed.emit(test_name, result, details)
        
    def print_test_summary(self):
        """طباعة ملخص الاختبارات"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["result"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 ملخص الاختبارات:")
        print(f"   إجمالي الاختبارات: {total_tests}")
        print(f"   ✅ نجح: {passed_tests}")
        print(f"   ❌ فشل: {failed_tests}")
        print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ الاختبارات الفاشلة:")
            for test_name, result in self.test_results.items():
                if not result["result"]:
                    print(f"   • {test_name}: {result['details']}")

class TestWindow(QMainWindow):
    """نافذة الاختبار"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_test_suite()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🧪 اختبار النظام المتجاوب الشامل")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان
        from PyQt5.QtWidgets import QLabel
        title = QLabel("🧪 اختبار النظام المتجاوب الشامل")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)
        
        # منطقة النتائج
        self.results_area = QLabel("جاري تشغيل الاختبارات...")
        self.results_area.setAlignment(Qt.AlignTop)
        self.results_area.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.results_area)
        
    def setup_test_suite(self):
        """إعداد مجموعة الاختبارات"""
        self.test_suite = ResponsiveTestSuite()
        self.test_suite.test_completed.connect(self.on_test_completed)
        self.test_suite.all_tests_completed.connect(self.on_all_tests_completed)
        
        # بدء الاختبارات بعد ثانية
        QTimer.singleShot(1000, self.test_suite.start)
        
    def on_test_completed(self, test_name, result, details):
        """معالج اكتمال اختبار واحد"""
        status = "✅ نجح" if result else "❌ فشل"
        current_text = self.results_area.text()
        
        if current_text == "جاري تشغيل الاختبارات...":
            current_text = ""
            
        new_text = f"{current_text}\n{status} {test_name}: {details}"
        self.results_area.setText(new_text)
        
    def on_all_tests_completed(self, results):
        """معالج اكتمال جميع الاختبارات"""
        total = len(results)
        passed = sum(1 for r in results.values() if r["result"])
        
        summary = f"\n\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح"
        
        if passed == total:
            summary += "\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام."
        else:
            summary += f"\n⚠️ {total-passed} اختبار فشل. يرجى مراجعة التفاصيل أعلاه."
            
        current_text = self.results_area.text()
        self.results_area.setText(current_text + summary)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تشغيل الاختبارات في وضع النافذة
    window = TestWindow()
    window.show()
    
    print("🧪 تم تشغيل نافذة الاختبار الشامل")
    print("📊 ستظهر النتائج في النافذة وفي وحدة التحكم")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
