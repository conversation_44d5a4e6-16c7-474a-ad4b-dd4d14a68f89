from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QComboBox, QTextEdit, QPushButton,
                             QFrame, QGridLayout, QDateEdit, QMessageBox,
                             QDoubleSpinBox, QFormLayout, QSpacerItem, QSizePolicy,
                             QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QDoubleValidator
from ai_assistant import AIAssistant

class AddExpensePage(QWidget):
    """صفحة إضافة مصروف جديد"""
    
    # إشارة لإعلام النافذة الرئيسية بإضافة مصروف جديد
    expense_added = pyqtSignal()
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.ai_assistant = AIAssistant(database)
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان الرئيسي
        title = QLabel("➕ إضافة مصروف جديد")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignRight)
        layout.addWidget(title)
        
        # إنشاء النموذج
        self.create_form(layout)
        
        # أزرار العمليات
        self.create_action_buttons(layout)
        
        # إضافة مساحة فارغة في الأسفل
        layout.addStretch()
        
    def create_form(self, parent_layout):
        """إنشاء نموذج إدخال البيانات"""
        form_frame = QFrame()
        form_frame.setObjectName("card")
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(20)
        form_layout.setContentsMargins(30, 30, 30, 30)
        
        # اسم المصروف
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("مثال: غداء في المطعم")
        name_label = QLabel("اسم المصروف:")
        name_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        form_layout.addRow(name_label, self.name_input)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        self.amount_input.setStyleSheet("""
            QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ffffff;
                color: #2c3e50;
                min-height: 20px;
            }
            QDoubleSpinBox:focus {
                border-color: #3498db;
            }
        """)
        amount_label = QLabel("المبلغ:")
        amount_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        form_layout.addRow(amount_label, self.amount_input)
        
        # التصنيف
        self.category_combo = QComboBox()
        self.load_categories()
        category_label = QLabel("التصنيف:")
        category_label.setFont(QFont("Segoe UI", 12, QFont.Bold))

        # إضافة خيار التصنيف التلقائي
        category_layout = QHBoxLayout()
        category_layout.addWidget(self.category_combo)

        self.auto_categorize_btn = QPushButton("تصنيف تلقائي")
        self.auto_categorize_btn.setObjectName("secondary_button")
        self.auto_categorize_btn.clicked.connect(self.auto_categorize)
        category_layout.addWidget(self.auto_categorize_btn)

        form_layout.addRow(category_label, category_layout)
        
        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        self.date_input.setDisplayFormat("yyyy-MM-dd")
        self.date_input.setStyleSheet("""
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ffffff;
                color: #2c3e50;
                min-height: 20px;
            }
            QDateEdit:focus {
                border-color: #3498db;
            }
            QDateEdit::drop-down {
                border: none;
                width: 30px;
            }
        """)
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        form_layout.addRow(date_label, self.date_input)
        
        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_input.setMaximumHeight(100)
        notes_label = QLabel("الملاحظات:")
        notes_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        form_layout.addRow(notes_label, self.notes_input)
        
        parent_layout.addWidget(form_frame)
        
    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار العمليات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ المصروف")
        save_btn.setObjectName("success_btn")
        save_btn.clicked.connect(self.save_expense)
        save_btn.setMinimumHeight(50)
        save_btn.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # زر المسح
        clear_btn = QPushButton("🗑️ مسح النموذج")
        clear_btn.setObjectName("warning_btn")
        clear_btn.clicked.connect(self.clear_form)
        clear_btn.setMinimumHeight(50)
        clear_btn.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # إضافة مساحة فارغة
        buttons_layout.addStretch()
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addWidget(save_btn)
        
        parent_layout.addWidget(buttons_frame)
        
    def load_categories(self):
        """تحميل التصنيفات من قاعدة البيانات"""
        categories = self.db.get_categories()
        self.category_combo.clear()
        
        for category in categories:
            self.category_combo.addItem(category['name'])
            
    def validate_form(self):
        """التحقق من صحة البيانات المدخلة"""
        errors = []
        
        # التحقق من اسم المصروف
        if not self.name_input.text().strip():
            errors.append("يجب إدخال اسم المصروف")
            
        # التحقق من المبلغ
        if self.amount_input.value() <= 0:
            errors.append("يجب إدخال مبلغ أكبر من صفر")
            
        # التحقق من التصنيف
        if not self.category_combo.currentText():
            errors.append("يجب اختيار تصنيف")
            
        return errors
        
    def save_expense(self):
        """حفظ المصروف الجديد"""
        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            error_message = "\n".join(errors)
            QMessageBox.warning(self, "خطأ في البيانات", error_message)
            return
            
        # جمع البيانات
        name = self.name_input.text().strip()
        amount = self.amount_input.value()
        category = self.category_combo.currentText()
        date = self.date_input.date().toString("yyyy-MM-dd")
        notes = self.notes_input.toPlainText().strip()
        
        # حفظ في قاعدة البيانات
        success = self.db.add_expense(name, amount, category, date, notes)
        
        if success:
            QMessageBox.information(self, "نجح الحفظ", 
                                  f"تم حفظ المصروف '{name}' بنجاح!")
            self.clear_form()
            self.expense_added.emit()  # إرسال إشارة التحديث
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ المصروف!")

    def auto_categorize(self):
        """تصنيف تلقائي للمصروف بناءً على الاسم والمبلغ"""
        name = self.name_input.text().strip()
        amount = self.amount_input.value()

        if not name:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المصروف أولاً")
            return

        # الحصول على التصنيف المقترح
        suggested_category = self.ai_assistant.auto_categorize_expense(name, amount)

        # البحث عن التصنيف في القائمة وتحديده
        for i in range(self.category_combo.count()):
            if self.category_combo.itemText(i) == suggested_category:
                self.category_combo.setCurrentIndex(i)
                QMessageBox.information(self, "تصنيف تلقائي", f"تم اقتراح التصنيف: {suggested_category}")
                return

        # إذا لم يوجد التصنيف، أظهر رسالة
        QMessageBox.information(self, "تصنيف تلقائي", f"التصنيف المقترح: {suggested_category}\nلم يتم العثور عليه في القائمة")

    def clear_form(self):
        """مسح جميع حقول النموذج"""
        self.name_input.clear()
        self.amount_input.setValue(0.0)
        self.category_combo.setCurrentIndex(0)
        self.date_input.setDate(QDate.currentDate())
        self.notes_input.clear()
        
        # التركيز على حقل الاسم
        self.name_input.setFocus()
        
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        # حفظ بالضغط على Ctrl+S
        if event.key() == Qt.Key_S and event.modifiers() == Qt.ControlModifier:
            self.save_expense()
        # مسح بالضغط على Ctrl+R
        elif event.key() == Qt.Key_R and event.modifiers() == Qt.ControlModifier:
            self.clear_form()
        else:
            super().keyPressEvent(event)
