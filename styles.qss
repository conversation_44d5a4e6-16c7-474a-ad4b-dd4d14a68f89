/* 🎨 نظام التصميم المتجاوب المتقدم - إدارة المصاريف الشخصية */
/* =====================================================================
   نظام متغيرات CSS متقدم مع دعم كامل للتجاوب
   يدعم جميع أحجام الشاشات من الهاتف إلى سطح المكتب
   ===================================================================== */

/* 🌈 نظام الألوان المحسن */
* {
    /* الألوان الأساسية */
    qproperty-primaryColor: #2c3e50;      /* الأزرق الداكن */
    qproperty-secondaryColor: #3498db;    /* الأزرق الفاتح */
    qproperty-accentColor: #e74c3c;       /* الأحمر */
    qproperty-successColor: #27ae60;      /* الأخضر */
    qproperty-warningColor: #f39c12;      /* البرتقالي */
    qproperty-infoColor: #9b59b6;         /* البنفسجي */

    /* الألوان المحايدة */
    qproperty-backgroundLight: #ffffff;
    qproperty-backgroundDark: #f8f9fa;
    qproperty-backgroundGray: #ecf0f1;
    qproperty-textPrimary: #2c3e50;
    qproperty-textSecondary: #7f8c8d;
    qproperty-textMuted: #95a5a6;
    qproperty-borderColor: #ecf0f1;
    qproperty-borderHover: #bdc3c7;
    qproperty-shadowColor: rgba(0,0,0,0.1);
    qproperty-shadowHover: rgba(0,0,0,0.15);

    /* ألوان الحالات */
    qproperty-dangerColor: #e74c3c;
    qproperty-dangerHover: #c0392b;
    qproperty-successHover: #229954;
    qproperty-warningHover: #e67e22;
    qproperty-infoHover: #8e44ad;
}

/* 📐 النافذة الرئيسية المتجاوبة */
QMainWindow {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
}

/* 🏗️ نظام الشبكة المتجاوب المتقدم */
/* ================================================= */

/* الحاويات المرنة */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

.container-fluid {
    width: 100%;
    padding: 0 16px;
}

.container-sm {
    max-width: 576px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 992px;
}

.container-xl {
    max-width: 1200px;
}

/* نظام الصفوف والأعمدة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
}

.col {
    flex: 1;
    padding: 0 8px;
    min-width: 0;
}

/* الأعمدة بنسب محددة */
.col-1 { flex: 0 0 8.333%; max-width: 8.333%; }
.col-2 { flex: 0 0 16.666%; max-width: 16.666%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333%; max-width: 33.333%; }
.col-5 { flex: 0 0 41.666%; max-width: 41.666%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333%; max-width: 58.333%; }
.col-8 { flex: 0 0 66.666%; max-width: 66.666%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333%; max-width: 83.333%; }
.col-11 { flex: 0 0 91.666%; max-width: 91.666%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* 📱 الشريط الجانبي المتجاوب */
#sidebar {
    background-color: #34495e;
    border: none;
    min-width: 250px;
    max-width: 250px;
    transition: all 0.3s ease;
}

/* 🔘 أزرار الشريط الجانبي المتجاوبة */
#sidebar QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    padding: 15px 20px;
    text-align: right;
    font-size: 14px;
    font-weight: 500;
    border-radius: 0px;
    transition: all 0.3s ease;
    min-height: 44px; /* للمس السهل */
}

#sidebar QPushButton:hover {
    background-color: #3498db;
    color: white;
    transform: translateX(-2px);
}

#sidebar QPushButton:pressed {
    background-color: #2980b9;
    transform: translateX(0px);
}

#sidebar QPushButton:checked {
    background-color: #3498db;
    color: white;
    border-left: 4px solid #e74c3c;
    font-weight: 600;
}

/* 🎯 نظام الأزرار المتجاوب المتقدم */
/* ============================================ */

/* الأزرار الأساسية */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    min-height: 44px; /* للمس السهل على الهاتف */
    transition: all 0.3s ease;
    text-align: center;
}

QPushButton:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

QPushButton:pressed {
    background-color: #21618c;
    transform: translateY(0px);
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
    transform: none;
    box-shadow: none;
}

/* أحجام الأزرار المتجاوبة */
QPushButton.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 36px;
    border-radius: 6px;
}

QPushButton.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    min-height: 52px;
    border-radius: 10px;
    font-weight: 600;
}

QPushButton.btn-xl {
    padding: 20px 40px;
    font-size: 18px;
    min-height: 60px;
    border-radius: 12px;
    font-weight: 600;
}

/* أنواع الأزرار المتخصصة */
QPushButton#success_btn, QPushButton.btn-success {
    background-color: #27ae60;
}

QPushButton#success_btn:hover, QPushButton.btn-success:hover {
    background-color: #229954;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

QPushButton#danger_btn, QPushButton.btn-danger {
    background-color: #e74c3c;
}

QPushButton#danger_btn:hover, QPushButton.btn-danger:hover {
    background-color: #c0392b;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

QPushButton#warning_btn, QPushButton.btn-warning {
    background-color: #f39c12;
    color: white;
}

QPushButton#warning_btn:hover, QPushButton.btn-warning:hover {
    background-color: #e67e22;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

QPushButton.btn-info {
    background-color: #9b59b6;
}

QPushButton.btn-info:hover {
    background-color: #8e44ad;
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

QPushButton.btn-secondary {
    background-color: #95a5a6;
}

QPushButton.btn-secondary:hover {
    background-color: #7f8c8d;
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

QPushButton.btn-outline {
    background-color: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

QPushButton.btn-outline:hover {
    background-color: #3498db;
    color: white;
    transform: translateY(-1px);
}

/* 📱 المنطقة الرئيسية المتجاوبة */
#main_content {
    background-color: #ffffff;
    border: none;
    border-radius: 12px;
    margin: 10px;
    transition: all 0.3s ease;
}

/* 📝 نظام العناوين المتجاوب */
/* ================================ */

QLabel#title, QLabel.page-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    padding: 20px;
    background-color: transparent;
    line-height: 1.2;
}

QLabel#subtitle, QLabel.page-subtitle {
    font-size: 18px;
    color: #7f8c8d;
    padding: 10px 20px;
    font-weight: 400;
    line-height: 1.4;
}

QLabel.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    padding: 15px 0;
}

QLabel.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

/* 🃏 نظام البطاقات المتجاوب المتقدم */
/* ======================================= */

/* البطاقات الأساسية */
QFrame#card, QFrame.card {
    background-color: #ffffff;
    border: 1px solid #ecf0f1;
    border-radius: 12px;
    padding: 20px;
    margin: 8px;
    transition: all 0.3s ease;
}

QFrame#card:hover, QFrame.card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 16px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

/* بطاقات الإحصائيات */
QFrame#stat_card, QFrame.stat-card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 15px;
    padding: 20px;
    margin: 8px;
    min-width: 200px;
    min-height: 120px;
    transition: all 0.3s ease;
}

QFrame#stat_card:hover, QFrame.stat-card:hover {
    border-color: #3498db;
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.15);
    transform: translateY(-3px);
}

QFrame#stat_card QLabel, QFrame.stat-card QLabel {
    color: #2c3e50;
    font-weight: 600;
}

/* 📝 نظام النماذج المتجاوب المتقدم */
/* ===================================== */

/* حقول الإدخال الأساسية */
QLineEdit {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 14px 16px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
    transition: all 0.3s ease;
    min-height: 20px;
}

QLineEdit:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background-color: #ffffff;
}

QLineEdit:hover {
    border-color: #bdc3c7;
}

QLineEdit:disabled {
    background-color: #f8f9fa;
    color: #95a5a6;
    border-color: #ecf0f1;
}

/* حقول الإدخال الكبيرة */
QLineEdit.input-lg {
    padding: 16px 20px;
    font-size: 16px;
    border-radius: 10px;
    min-height: 24px;
}

/* حقول الإدخال الصغيرة */
QLineEdit.input-sm {
    padding: 10px 12px;
    font-size: 12px;
    border-radius: 6px;
    min-height: 16px;
}

/* مربعات النص الكبيرة */
QTextEdit {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 14px 16px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
    transition: all 0.3s ease;
    line-height: 1.5;
}

QTextEdit:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

QTextEdit:hover {
    border-color: #bdc3c7;
}

/* 📋 القوائم المنسدلة المتجاوبة */
/* ================================= */

QComboBox {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    padding: 14px 16px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
    min-height: 20px;
    transition: all 0.3s ease;
}

QComboBox:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

QComboBox:hover {
    border-color: #bdc3c7;
}

QComboBox::drop-down {
    border: none;
    width: 32px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #7f8c8d;
    margin-right: 8px;
}

QComboBox::down-arrow:hover {
    border-top-color: #3498db;
}

QComboBox QAbstractItemView {
    border: 2px solid #3498db;
    border-radius: 8px;
    background-color: #ffffff;
    selection-background-color: #3498db;
    selection-color: white;
    padding: 8px;
    outline: none;
}

QComboBox QAbstractItemView::item {
    padding: 10px 12px;
    border-radius: 4px;
    margin: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: #ecf0f1;
    color: #2c3e50;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #3498db;
    color: white;
}

/* أحجام القوائم المنسدلة */
QComboBox.combo-lg {
    padding: 16px 20px;
    font-size: 16px;
    min-height: 24px;
}

QComboBox.combo-sm {
    padding: 10px 12px;
    font-size: 12px;
    min-height: 16px;
}

/* 📊 نظام الجداول المتجاوب المتقدم */
/* ==================================== */

/* الجداول الأساسية */
QTableWidget {
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
    font-size: 14px;
    outline: none;
}

QTableWidget::item {
    padding: 12px 16px;
    border: none;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QTableWidget::item:hover {
    background-color: #ecf0f1;
    color: #2c3e50;
}

/* رؤوس الجداول */
QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 14px 16px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
}

QHeaderView::section:hover {
    background-color: #3498db;
}

QHeaderView::section:pressed {
    background-color: #2980b9;
}

/* الجداول المضغوطة */
QTableWidget.table-sm {
    font-size: 12px;
}

QTableWidget.table-sm::item {
    padding: 8px 12px;
}

QTableWidget.table-sm QHeaderView::section {
    padding: 10px 12px;
    font-size: 12px;
}

/* الجداول الكبيرة */
QTableWidget.table-lg {
    font-size: 16px;
}

QTableWidget.table-lg::item {
    padding: 16px 20px;
}

QTableWidget.table-lg QHeaderView::section {
    padding: 18px 20px;
    font-size: 16px;
}

/* 📜 نظام شريط التمرير المحسن */
/* ================================ */

/* شريط التمرير العمودي */
QScrollBar:vertical {
    border: none;
    background-color: #f8f9fa;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::handle:vertical:pressed {
    background-color: #7f8c8d;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* شريط التمرير الأفقي */
QScrollBar:horizontal {
    border: none;
    background-color: #f8f9fa;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-width: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #7f8c8d;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 0;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}

/* 📊 شريط التقدم المتجاوب */
/* ========================== */

QProgressBar {
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    background-color: #f8f9fa;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: #2c3e50;
    height: 24px;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 8px;
    margin: 2px;
}

QProgressBar.progress-success::chunk {
    background-color: #27ae60;
}

QProgressBar.progress-warning::chunk {
    background-color: #f39c12;
}

QProgressBar.progress-danger::chunk {
    background-color: #e74c3c;
}

QProgressBar.progress-info::chunk {
    background-color: #9b59b6;
}

/* أحجام شريط التقدم */
QProgressBar.progress-sm {
    height: 16px;
    font-size: 12px;
}

QProgressBar.progress-lg {
    height: 32px;
    font-size: 16px;
    border-radius: 16px;
}

QProgressBar.progress-lg::chunk {
    border-radius: 14px;
}

/* ☑️ مربعات الاختيار المحسنة */
/* ============================= */

QCheckBox {
    font-size: 14px;
    color: #2c3e50;
    spacing: 10px;
    font-weight: 500;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background-color: #ffffff;
}

QCheckBox::indicator:hover {
    border-color: #3498db;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
    image: none;
}

QCheckBox::indicator:checked:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

QCheckBox::indicator:disabled {
    background-color: #ecf0f1;
    border-color: #bdc3c7;
}

/* الجداول */
QTableWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
}

QTableWidget::item {
    padding: 12px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 12px;
    border: none;
    font-weight: bold;
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* التقويم */
QCalendarWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QCalendarWidget QToolButton {
    background-color: #34495e;
    color: white;
    border: none;
    padding: 8px;
    font-weight: bold;
}

QCalendarWidget QToolButton:hover {
    background-color: #3498db;
}

QCalendarWidget QAbstractItemView {
    selection-background-color: #3498db;
    selection-color: white;
}

/* رسائل التنبيه */
QMessageBox {
    background-color: #ffffff;
    color: #2c3e50;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 8px 16px;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ecf0f1;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 6px;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #ecf0f1;
    color: #2c3e50;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QTabBar::tab:hover {
    background-color: #bdc3c7;
}

/* أنماط صفحة الميزانيات */
#budget_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
}

#budget_card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

#card_title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

#percentage_label {
    font-size: 14px;
    font-weight: bold;
    padding: 5px;
    border-radius: 5px;
    background-color: #ecf0f1;
}

#date_selector {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

/* أنماط صفحة الأهداف */
#goal_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 20px;
    margin: 8px;
}

#goal_card:hover {
    border-color: #27ae60;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.2);
}

#goal_name {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
}

#stat_card {
    background-color: #ffffff;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 15px;
    margin: 5px;
}

#stat_title {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: normal;
}

#stat_value {
    font-size: 18px;
    font-weight: bold;
    margin-top: 5px;
}

#chart_title {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
}

#toolbar {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
}

/* أزرار إضافية */
QPushButton#primary_button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 13px;
}

QPushButton#primary_button:hover {
    background-color: #2980b9;
}

QPushButton#primary_button:pressed {
    background-color: #21618c;
}

QPushButton#secondary_button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 12px;
}

QPushButton#secondary_button:hover {
    background-color: #7f8c8d;
}

QPushButton#secondary_button:pressed {
    background-color: #6c7b7d;
}

QPushButton#danger_button {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 12px;
}

QPushButton#danger_button:hover {
    background-color: #c0392b;
}

QPushButton#danger_button:pressed {
    background-color: #a93226;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    background-color: #f8f9fa;
    height: 25px;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 6px;
}

/* تحسينات إضافية للتجاوب */
QScrollArea {
    border: none;
    background-color: transparent;
}

QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* تحسينات للجداول */
QTableWidget {
    gridline-color: #ecf0f1;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    selection-background-color: #3498db;
    selection-color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
}

/* أنماط صفحة التنبيهات */
#notification_card {
    background-color: #ffffff;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    padding: 15px;
    margin: 5px;
}

#notification_card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#notification_icon {
    font-size: 20px;
    margin-right: 10px;
}

#notification_title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
}

#notification_time {
    font-size: 12px;
    color: #7f8c8d;
    font-style: italic;
}

#notification_message {
    font-size: 14px;
    color: #34495e;
    margin: 10px 0;
    line-height: 1.4;
}

#unread_counter {
    font-size: 14px;
    font-weight: bold;
    color: #e74c3c;
    background-color: #fdf2f2;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #fadbd8;
}

/* تحسينات إضافية للتفاعل */
QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
    border: 1px solid #95a5a6;
}

QComboBox {
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    font-size: 13px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #7f8c8d;
    margin-right: 5px;
}

QSpinBox, QDoubleSpinBox {
    border: 2px solid #ecf0f1;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    font-size: 13px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #3498db;
}

QCheckBox {
    font-size: 13px;
    color: #2c3e50;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
    image: none;
}

QCheckBox::indicator:checked:after {
    content: "✓";
    color: white;
    font-weight: bold;
}
