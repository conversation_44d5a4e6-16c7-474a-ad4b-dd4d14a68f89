#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات مساعدة للتصميم المتجاوب
تحتوي على دوال مساعدة لتطبيق التحسينات المتجاوبة
"""

from PyQt5.QtWidgets import QWidget, QLayout, QGridLayout, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import QSize, QRect
from PyQt5.QtGui import QFont

class ResponsiveUtils:
    """أدوات مساعدة للتصميم المتجاوب"""
    
    @staticmethod
    def apply_responsive_font(widget, base_size=14, size_category='md'):
        """تطبيق حجم خط متجاوب"""
        multipliers = {
            'xs': 0.85,
            'sm': 0.9,
            'md': 1.0,
            'lg': 1.1,
            'xl': 1.2
        }
        
        font_size = int(base_size * multipliers.get(size_category, 1.0))
        font = widget.font()
        font.setPointSize(font_size)
        widget.setFont(font)
    
    @staticmethod
    def apply_responsive_margins(widget, base_margin=10, size_category='md'):
        """تطبيق هوامش متجاوبة"""
        multipliers = {
            'xs': 0.4,
            'sm': 0.6,
            'md': 1.0,
            'lg': 1.2,
            'xl': 1.5
        }
        
        margin = int(base_margin * multipliers.get(size_category, 1.0))
        widget.setContentsMargins(margin, margin, margin, margin)
    
    @staticmethod
    def apply_responsive_spacing(layout, base_spacing=10, size_category='md'):
        """تطبيق مسافات متجاوبة"""
        multipliers = {
            'xs': 0.5,
            'sm': 0.7,
            'md': 1.0,
            'lg': 1.3,
            'xl': 1.6
        }
        
        spacing = int(base_spacing * multipliers.get(size_category, 1.0))
        if hasattr(layout, 'setSpacing'):
            layout.setSpacing(spacing)
    
    @staticmethod
    def rearrange_grid_layout(layout, items, columns):
        """إعادة ترتيب تخطيط الشبكة"""
        try:
            if not isinstance(layout, QGridLayout):
                return
            
            # مسح التخطيط الحالي
            for i in reversed(range(layout.count())):
                item = layout.itemAt(i)
                if item and item.widget():
                    layout.removeWidget(item.widget())
            
            # إعادة ترتيب العناصر
            for i, widget in enumerate(items):
                if widget:
                    row = i // columns
                    col = i % columns
                    layout.addWidget(widget, row, col)
                    
        except Exception as e:
            print(f"خطأ في إعادة ترتيب الشبكة: {e}")
    
    @staticmethod
    def convert_layout_to_vertical(parent_widget, widgets):
        """تحويل التخطيط إلى عمودي"""
        try:
            # إنشاء تخطيط عمودي جديد
            layout = QVBoxLayout()
            
            # إضافة الويدجت
            for widget in widgets:
                if widget:
                    layout.addWidget(widget)
            
            # تطبيق التخطيط
            parent_widget.setLayout(layout)
            
        except Exception as e:
            print(f"خطأ في تحويل التخطيط: {e}")
    
    @staticmethod
    def convert_layout_to_horizontal(parent_widget, widgets):
        """تحويل التخطيط إلى أفقي"""
        try:
            # إنشاء تخطيط أفقي جديد
            layout = QHBoxLayout()
            
            # إضافة الويدجت
            for widget in widgets:
                if widget:
                    layout.addWidget(widget)
            
            # تطبيق التخطيط
            parent_widget.setLayout(layout)
            
        except Exception as e:
            print(f"خطأ في تحويل التخطيط: {e}")
    
    @staticmethod
    def apply_touch_friendly_sizes(widget, size_category='md'):
        """تطبيق أحجام مناسبة للمس"""
        if size_category in ['xs', 'sm']:
            # أحجام أكبر للأجهزة اللمسية
            widget.setMinimumHeight(44)
            
            # تحسين الأزرار
            if hasattr(widget, 'setMinimumSize'):
                widget.setMinimumSize(44, 44)
    
    @staticmethod
    def optimize_for_mobile(widget, size_category):
        """تحسينات خاصة بالهاتف"""
        if size_category in ['xs', 'sm']:
            # إخفاء عناصر غير ضرورية
            if hasattr(widget, 'setVisible'):
                # يمكن إضافة منطق لإخفاء عناصر معينة
                pass
            
            # تحسين التمرير
            if hasattr(widget, 'setVerticalScrollBarPolicy'):
                from PyQt5.QtCore import Qt
                widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
    
    @staticmethod
    def get_responsive_icon_size(size_category='md'):
        """الحصول على حجم الأيقونة المتجاوب"""
        sizes = {
            'xs': QSize(16, 16),
            'sm': QSize(20, 20),
            'md': QSize(24, 24),
            'lg': QSize(28, 28),
            'xl': QSize(32, 32)
        }
        
        return sizes.get(size_category, QSize(24, 24))
    
    @staticmethod
    def get_responsive_button_size(size_category='md'):
        """الحصول على حجم الزر المتجاوب"""
        sizes = {
            'xs': QSize(120, 36),
            'sm': QSize(140, 40),
            'md': QSize(160, 44),
            'lg': QSize(180, 48),
            'xl': QSize(200, 52)
        }
        
        return sizes.get(size_category, QSize(160, 44))
    
    @staticmethod
    def apply_responsive_card_style(card_widget, size_category='md'):
        """تطبيق أنماط البطاقة المتجاوبة"""
        try:
            # تطبيق الفئة المناسبة
            card_widget.setProperty("responsive-size", size_category)
            
            # إعادة تطبيق الأنماط
            card_widget.style().unpolish(card_widget)
            card_widget.style().polish(card_widget)
            
        except Exception as e:
            print(f"خطأ في تطبيق أنماط البطاقة: {e}")
    
    @staticmethod
    def create_responsive_spacer(size_category='md'):
        """إنشاء فاصل متجاوب"""
        from PyQt5.QtWidgets import QSpacerItem, QSizePolicy
        
        sizes = {
            'xs': 5,
            'sm': 8,
            'md': 15,
            'lg': 20,
            'xl': 25
        }
        
        size = sizes.get(size_category, 15)
        return QSpacerItem(size, size, QSizePolicy.Minimum, QSizePolicy.Expanding)
    
    @staticmethod
    def apply_responsive_table_style(table_widget, size_category='md'):
        """تطبيق أنماط الجدول المتجاوبة"""
        try:
            if size_category in ['xs', 'sm']:
                # تحسينات للهاتف
                table_widget.setProperty("mobile-view", "true")
                
                # تقليل حجم الخط
                font = table_widget.font()
                font.setPointSize(12)
                table_widget.setFont(font)
                
                # تقليل ارتفاع الصفوف
                table_widget.verticalHeader().setDefaultSectionSize(30)
                
            else:
                # إعدادات سطح المكتب
                table_widget.setProperty("mobile-view", "false")
                
                # حجم خط عادي
                font = table_widget.font()
                font.setPointSize(14)
                table_widget.setFont(font)
                
                # ارتفاع صفوف عادي
                table_widget.verticalHeader().setDefaultSectionSize(40)
            
            # إعادة تطبيق الأنماط
            table_widget.style().unpolish(table_widget)
            table_widget.style().polish(table_widget)
            
        except Exception as e:
            print(f"خطأ في تطبيق أنماط الجدول: {e}")

class ResponsiveBreakpoints:
    """نقاط التوقف للتصميم المتجاوب"""
    
    XS = 480    # هاتف صغير
    SM = 768    # هاتف كبير
    MD = 1024   # تابلت
    LG = 1440   # سطح مكتب
    XL = 1920   # شاشة كبيرة
    
    @classmethod
    def get_size_category(cls, width):
        """الحصول على فئة الحجم من العرض"""
        if width <= cls.XS:
            return 'xs'
        elif width <= cls.SM:
            return 'sm'
        elif width <= cls.MD:
            return 'md'
        elif width <= cls.LG:
            return 'lg'
        else:
            return 'xl'
    
    @classmethod
    def is_mobile(cls, width):
        """فحص إذا كان العرض للهاتف"""
        return width <= cls.SM
    
    @classmethod
    def is_tablet(cls, width):
        """فحص إذا كان العرض للتابلت"""
        return cls.SM < width <= cls.MD
    
    @classmethod
    def is_desktop(cls, width):
        """فحص إذا كان العرض لسطح المكتب"""
        return width > cls.MD
