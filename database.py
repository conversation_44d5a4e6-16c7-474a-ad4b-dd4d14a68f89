import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class ExpenseDatabase:
    """كلاس للتعامل مع قاعدة بيانات المصاريف"""
    
    def __init__(self, db_path: str = "expenses.db"):
        self.db_path = db_path
        self.init_database()

    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # إنشاء جدول المصاريف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    amount REAL NOT NULL,
                    category TEXT NOT NULL,
                    date TEXT NOT NULL,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول التصنيفات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    color TEXT DEFAULT '#3498db'
                )
            ''')

            # إنشاء جدول الميزانيات الشهرية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS monthly_budgets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    budget_amount REAL NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(category, year, month)
                )
            ''')

            # إنشاء جدول الأهداف المالية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS financial_goals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    target_amount REAL NOT NULL,
                    current_amount REAL DEFAULT 0,
                    target_date TEXT,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إنشاء جدول التنبيهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    type TEXT NOT NULL,
                    is_read INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # إدراج التصنيفات الافتراضية
            default_categories = [
                ('طعام وشراب', '#e74c3c'),
                ('مواصلات', '#3498db'),
                ('ترفيه', '#9b59b6'),
                ('صحة', '#2ecc71'),
                ('تسوق', '#f39c12'),
                ('فواتير', '#34495e'),
                ('تعليم', '#1abc9c'),
                ('أخرى', '#95a5a6')
            ]

            cursor.executemany('''
                INSERT OR IGNORE INTO categories (name, color) VALUES (?, ?)
            ''', default_categories)

            conn.commit()
    
    def add_expense(self, name: str, amount: float, category: str, 
                   date: str, notes: str = "") -> bool:
        """إضافة مصروف جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO expenses (name, amount, category, date, notes)
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, amount, category, date, notes))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في إضافة المصروف: {e}")
            return False
    
    def get_expenses(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """جلب المصاريف مع إمكانية التصفية بالتاريخ"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT * FROM expenses 
                        WHERE date BETWEEN ? AND ?
                        ORDER BY date DESC
                    ''', (start_date, end_date))
                else:
                    cursor.execute('SELECT * FROM expenses ORDER BY date DESC')
                
                columns = [description[0] for description in cursor.description]
                expenses = []
                
                for row in cursor.fetchall():
                    expense = dict(zip(columns, row))
                    expenses.append(expense)
                
                return expenses
        except Exception as e:
            print(f"خطأ في جلب المصاريف: {e}")
            return []
    
    def update_expense(self, expense_id: int, name: str, amount: float, 
                      category: str, date: str, notes: str = "") -> bool:
        """تحديث مصروف موجود"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE expenses 
                    SET name=?, amount=?, category=?, date=?, notes=?
                    WHERE id=?
                ''', (name, amount, category, date, notes, expense_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في تحديث المصروف: {e}")
            return False
    
    def delete_expense(self, expense_id: int) -> bool:
        """حذف مصروف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM expenses WHERE id=?', (expense_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في حذف المصروف: {e}")
            return False
    
    def get_categories(self) -> List[Dict]:
        """جلب جميع التصنيفات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM categories ORDER BY name')
                
                columns = [description[0] for description in cursor.description]
                categories = []
                
                for row in cursor.fetchall():
                    category = dict(zip(columns, row))
                    categories.append(category)
                
                return categories
        except Exception as e:
            print(f"خطأ في جلب التصنيفات: {e}")
            return []
    
    def get_expenses_by_category(self, start_date: str = None, 
                               end_date: str = None) -> Dict[str, float]:
        """جلب المصاريف مجمعة حسب التصنيف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT category, SUM(amount) as total
                        FROM expenses 
                        WHERE date BETWEEN ? AND ?
                        GROUP BY category
                        ORDER BY total DESC
                    ''', (start_date, end_date))
                else:
                    cursor.execute('''
                        SELECT category, SUM(amount) as total
                        FROM expenses 
                        GROUP BY category
                        ORDER BY total DESC
                    ''')
                
                result = {}
                for row in cursor.fetchall():
                    result[row[0]] = row[1]
                
                return result
        except Exception as e:
            print(f"خطأ في جلب المصاريف حسب التصنيف: {e}")
            return {}
    
    def get_total_expenses(self, start_date: str = None, 
                          end_date: str = None) -> float:
        """جلب إجمالي المصاريف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT SUM(amount) FROM expenses 
                        WHERE date BETWEEN ? AND ?
                    ''', (start_date, end_date))
                else:
                    cursor.execute('SELECT SUM(amount) FROM expenses')
                
                result = cursor.fetchone()[0]
                return result if result else 0.0
        except Exception as e:
            print(f"خطأ في جلب إجمالي المصاريف: {e}")
            return 0.0

    def get_expense_count(self, start_date: str = None, end_date: str = None) -> int:
        """الحصول على عدد المصاريف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if start_date and end_date:
                    cursor.execute('''
                        SELECT COUNT(*) FROM expenses
                        WHERE date BETWEEN ? AND ?
                    ''', (start_date, end_date))
                else:
                    cursor.execute('SELECT COUNT(*) FROM expenses')

                return cursor.fetchone()[0]
        except Exception as e:
            print(f"خطأ في الحصول على عدد المصاريف: {e}")
            return 0

    # ==================== دوال الميزانية ====================

    def set_monthly_budget(self, category: str, year: int, month: int, amount: float) -> bool:
        """تحديد ميزانية شهرية لتصنيف معين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO monthly_budgets
                    (category, year, month, budget_amount)
                    VALUES (?, ?, ?, ?)
                ''', (category, year, month, amount))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في تحديد الميزانية: {e}")
            return False

    def get_monthly_budget(self, category: str, year: int, month: int) -> float:
        """الحصول على الميزانية الشهرية لتصنيف معين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT budget_amount FROM monthly_budgets
                    WHERE category = ? AND year = ? AND month = ?
                ''', (category, year, month))
                result = cursor.fetchone()
                return result[0] if result else 0.0
        except Exception as e:
            print(f"خطأ في الحصول على الميزانية: {e}")
            return 0.0

    def get_all_monthly_budgets(self, year: int, month: int) -> List[Dict]:
        """الحصول على جميع الميزانيات الشهرية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT category, budget_amount FROM monthly_budgets
                    WHERE year = ? AND month = ?
                ''', (year, month))
                results = cursor.fetchall()
                return [{'category': row[0], 'budget': row[1]} for row in results]
        except Exception as e:
            print(f"خطأ في الحصول على الميزانيات: {e}")
            return []

    def get_budget_vs_spending(self, year: int, month: int) -> List[Dict]:
        """مقارنة الميزانية مع الإنفاق الفعلي"""
        try:
            # تحديد نطاق التاريخ للشهر
            start_date = f"{year}-{month:02d}-01"
            if month == 12:
                end_date = f"{year + 1}-01-01"
            else:
                end_date = f"{year}-{month + 1:02d}-01"

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # الحصول على الميزانيات والإنفاق الفعلي
                cursor.execute('''
                    SELECT
                        mb.category,
                        mb.budget_amount,
                        COALESCE(SUM(e.amount), 0) as spent_amount
                    FROM monthly_budgets mb
                    LEFT JOIN expenses e ON mb.category = e.category
                        AND e.date >= ? AND e.date < ?
                    WHERE mb.year = ? AND mb.month = ?
                    GROUP BY mb.category, mb.budget_amount
                ''', (start_date, end_date, year, month))

                results = cursor.fetchall()
                budget_data = []

                for row in results:
                    category, budget, spent = row
                    percentage = (spent / budget * 100) if budget > 0 else 0
                    remaining = budget - spent

                    budget_data.append({
                        'category': category,
                        'budget': budget,
                        'spent': spent,
                        'remaining': remaining,
                        'percentage': percentage,
                        'status': 'over' if spent > budget else 'warning' if percentage > 80 else 'good'
                    })

                return budget_data
        except Exception as e:
            print(f"خطأ في مقارنة الميزانية: {e}")
            return []

    # ==================== دوال الأهداف المالية ====================

    def add_financial_goal(self, name: str, target_amount: float, target_date: str = None,
                          description: str = "") -> bool:
        """إضافة هدف مالي جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO financial_goals
                    (name, target_amount, target_date, description)
                    VALUES (?, ?, ?, ?)
                ''', (name, target_amount, target_date, description))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في إضافة الهدف المالي: {e}")
            return False

    def update_goal_progress(self, goal_id: int, amount: float) -> bool:
        """تحديث تقدم الهدف المالي"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE financial_goals
                    SET current_amount = current_amount + ?
                    WHERE id = ?
                ''', (amount, goal_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في تحديث تقدم الهدف: {e}")
            return False

    def get_financial_goals(self, active_only: bool = True) -> List[Dict]:
        """الحصول على الأهداف المالية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if active_only:
                    cursor.execute('''
                        SELECT * FROM financial_goals
                        WHERE is_active = 1
                        ORDER BY created_at DESC
                    ''')
                else:
                    cursor.execute('''
                        SELECT * FROM financial_goals
                        ORDER BY created_at DESC
                    ''')

                columns = [description[0] for description in cursor.description]
                goals = []

                for row in cursor.fetchall():
                    goal = dict(zip(columns, row))
                    # حساب النسبة المئوية للتقدم
                    if goal['target_amount'] > 0:
                        goal['progress_percentage'] = (goal['current_amount'] / goal['target_amount']) * 100
                    else:
                        goal['progress_percentage'] = 0
                    goals.append(goal)

                return goals
        except Exception as e:
            print(f"خطأ في الحصول على الأهداف المالية: {e}")
            return []

    # ==================== دوال التنبيهات ====================

    def add_notification(self, title: str, message: str, notification_type: str = "info") -> bool:
        """إضافة تنبيه جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO notifications (title, message, type)
                    VALUES (?, ?, ?)
                ''', (title, message, notification_type))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في إضافة التنبيه: {e}")
            return False

    def get_notifications(self, unread_only: bool = False) -> List[Dict]:
        """الحصول على التنبيهات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if unread_only:
                    cursor.execute('''
                        SELECT * FROM notifications
                        WHERE is_read = 0
                        ORDER BY created_at DESC
                    ''')
                else:
                    cursor.execute('''
                        SELECT * FROM notifications
                        ORDER BY created_at DESC
                        LIMIT 50
                    ''')

                columns = [description[0] for description in cursor.description]
                notifications = []

                for row in cursor.fetchall():
                    notifications.append(dict(zip(columns, row)))

                return notifications
        except Exception as e:
            print(f"خطأ في الحصول على التنبيهات: {e}")
            return []

    def mark_notification_read(self, notification_id: int) -> bool:
        """تحديد التنبيه كمقروء"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE notifications
                    SET is_read = 1
                    WHERE id = ?
                ''', (notification_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في تحديث التنبيه: {e}")
            return False

    def check_budget_alerts(self, year: int, month: int) -> List[Dict]:
        """فحص تجاوز الميزانيات وإنشاء تنبيهات"""
        budget_data = self.get_budget_vs_spending(year, month)
        alerts = []

        for item in budget_data:
            if item['status'] == 'over':
                alert = {
                    'type': 'budget_exceeded',
                    'category': item['category'],
                    'message': f"تم تجاوز ميزانية {item['category']} بمقدار {item['spent'] - item['budget']:.2f} ريال"
                }
                alerts.append(alert)
            elif item['status'] == 'warning':
                alert = {
                    'type': 'budget_warning',
                    'category': item['category'],
                    'message': f"اقتربت من حد ميزانية {item['category']} ({item['percentage']:.1f}%)"
                }
                alerts.append(alert)

        return alerts
