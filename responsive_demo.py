#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي للنظام المتجاوب المتقدم
يعرض جميع الميزات والتحسينات المطبقة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QWidget, QPushButton, QLabel, QComboBox, QSlider
from PyQt5.QtCore import Qt

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from responsive_manager import ResponsiveManager
from theme_manager import ResponsiveThemeManager
from responsive_components import ResponsiveButton, ResponsiveCard, ResponsiveTable
from touch_optimization import apply_touch_optimizations_to_app

class ResponsiveDemoWindow(QMainWindow):
    """نافذة العرض التوضيحي"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_responsive_system()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🎨 عرض توضيحي للنظام المتجاوب المتقدم")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(320, 480)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان
        title = QLabel("🎨 النظام المتجاوب المتقدم")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50;")
        layout.addWidget(title)
        
        # أدوات التحكم
        self.create_controls(layout)
        
        # البطاقات التوضيحية
        self.create_demo_cards(layout)
        
        # الجدول التوضيحي
        self.create_demo_table(layout)
        
    def create_controls(self, parent_layout):
        """إنشاء أدوات التحكم"""
        controls_layout = QHBoxLayout()
        
        # اختيار الثيم
        theme_label = QLabel("الثيم:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "مظلم", "أزرق", "أخضر"])
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        
        # زر تبديل الوضع
        self.mode_btn = ResponsiveButton("وضع الهاتف", "secondary")
        self.mode_btn.clicked.connect(self.toggle_mobile_mode)
        
        controls_layout.addWidget(theme_label)
        controls_layout.addWidget(self.theme_combo)
        controls_layout.addStretch()
        controls_layout.addWidget(self.mode_btn)
        
        parent_layout.addLayout(controls_layout)
        
    def create_demo_cards(self, parent_layout):
        """إنشاء البطاقات التوضيحية"""
        cards_layout = QHBoxLayout()
        
        # بطاقة الميزات
        features_card = ResponsiveCard("🚀 الميزات", "تصميم متجاوب متقدم")
        features_card.add_widget(QLabel("• تكيف تلقائي مع جميع الأحجام"))
        features_card.add_widget(QLabel("• ثيمات متعددة"))
        features_card.add_widget(QLabel("• تحسينات اللمس"))
        
        # بطاقة الأداء
        performance_card = ResponsiveCard("⚡ الأداء", "محسن للسرعة")
        performance_card.add_widget(QLabel("• تحميل سريع"))
        performance_card.add_widget(QLabel("• استهلاك ذاكرة منخفض"))
        performance_card.add_widget(QLabel("• رسوم متحركة سلسة"))
        
        # بطاقة التوافق
        compatibility_card = ResponsiveCard("📱 التوافق", "يعمل في كل مكان")
        compatibility_card.add_widget(QLabel("• سطح المكتب"))
        compatibility_card.add_widget(QLabel("• التابلت"))
        compatibility_card.add_widget(QLabel("• الهاتف"))
        
        cards_layout.addWidget(features_card)
        cards_layout.addWidget(performance_card)
        cards_layout.addWidget(compatibility_card)
        
        parent_layout.addLayout(cards_layout)
        
    def create_demo_table(self, parent_layout):
        """إنشاء الجدول التوضيحي"""
        table = ResponsiveTable(3, 4)
        table.setHorizontalHeaderLabels(["الميزة", "الحالة", "التقييم", "الملاحظات"])
        
        # ملء البيانات
        data = [
            ["التجاوب", "✅ مفعل", "ممتاز", "يعمل على جميع الأحجام"],
            ["الثيمات", "✅ مفعل", "ممتاز", "4 ثيمات متاحة"],
            ["اللمس", "✅ مفعل", "جيد جداً", "محسن للأجهزة اللمسية"]
        ]
        
        for row, row_data in enumerate(data):
            for col, cell_data in enumerate(row_data):
                table.setItem(row, col, table.create_item(cell_data))
        
        parent_layout.addWidget(table)
        
    def setup_responsive_system(self):
        """إعداد النظام المتجاوب"""
        # مدير التجاوب
        self.responsive_manager = ResponsiveManager(self)
        self.responsive_manager.size_changed.connect(self.on_size_changed)
        
        # مدير الثيمات
        self.theme_manager = ResponsiveThemeManager()
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
        
        # تحسينات اللمس
        self.touch_components = apply_touch_optimizations_to_app(self)
        
    def change_theme(self, theme_text):
        """تغيير الثيم"""
        theme_map = {
            "فاتح": "light",
            "مظلم": "dark", 
            "أزرق": "blue",
            "أخضر": "green"
        }
        
        theme_name = theme_map.get(theme_text, "light")
        self.theme_manager.set_theme(theme_name)
        
    def toggle_mobile_mode(self):
        """تبديل وضع الهاتف"""
        current_size = self.responsive_manager.current_size
        if current_size in ['xs', 'sm']:
            self.resize(1200, 800)  # وضع سطح المكتب
            self.mode_btn.setText("وضع الهاتف")
        else:
            self.resize(375, 667)   # وضع الهاتف
            self.mode_btn.setText("وضع سطح المكتب")
            
    def on_size_changed(self, size_category):
        """معالج تغيير الحجم"""
        size_names = {
            'xs': 'هاتف صغير',
            'sm': 'هاتف كبير',
            'md': 'تابلت',
            'lg': 'سطح مكتب',
            'xl': 'شاشة كبيرة'
        }
        
        size_name = size_names.get(size_category, 'غير معروف')
        self.setWindowTitle(f"🎨 عرض توضيحي للنظام المتجاوب - {size_name}")
        
    def on_theme_changed(self, theme_name):
        """معالج تغيير الثيم"""
        print(f"تم تغيير الثيم إلى: {theme_name}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ResponsiveDemoWindow()
    window.show()
    
    print("🎨 تم تشغيل العرض التوضيحي للنظام المتجاوب")
    print("📱 جرب تغيير حجم النافذة أو الثيم لرؤية التحسينات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
