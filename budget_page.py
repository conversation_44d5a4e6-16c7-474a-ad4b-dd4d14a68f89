import sys
from datetime import datetime, date
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox,
                             QFormLayout, QMessageBox, QProgressBar, QFrame,
                             QScrollArea, QGridLayout, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.patches as patches

class BudgetPage(QWidget):
    """صفحة إدارة الميزانيات"""
    
    budget_updated = pyqtSignal()
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.init_ui()
        self.load_budget_data()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الصفحة
        title = QLabel("إدارة الميزانيات الشهرية")
        title.setObjectName("page_title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شريط اختيار الشهر والسنة
        self.create_date_selector(layout)
        
        # منطقة المحتوى الرئيسي
        main_content = QHBoxLayout()
        
        # الجانب الأيسر - إعداد الميزانيات
        left_panel = self.create_budget_setup_panel()
        main_content.addWidget(left_panel, 1)
        
        # الجانب الأيمن - عرض الميزانيات
        right_panel = self.create_budget_display_panel()
        main_content.addWidget(right_panel, 2)
        
        layout.addLayout(main_content)
        
    def create_date_selector(self, parent_layout):
        """إنشاء محدد التاريخ"""
        date_frame = QFrame()
        date_frame.setObjectName("date_selector")
        date_layout = QHBoxLayout(date_frame)
        
        # تسمية
        date_layout.addWidget(QLabel("عرض ميزانية:"))
        
        # اختيار الشهر
        self.month_combo = QComboBox()
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(self.current_month - 1)
        self.month_combo.currentIndexChanged.connect(self.on_date_changed)
        date_layout.addWidget(self.month_combo)
        
        # اختيار السنة
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(self.current_year)
        self.year_spin.valueChanged.connect(self.on_date_changed)
        date_layout.addWidget(self.year_spin)
        
        date_layout.addStretch()
        parent_layout.addWidget(date_frame)
        
    def create_budget_setup_panel(self):
        """إنشاء لوحة إعداد الميزانيات"""
        group = QGroupBox("إعداد الميزانيات")
        layout = QVBoxLayout(group)
        
        # نموذج إضافة ميزانية
        form_layout = QFormLayout()
        
        # اختيار التصنيف
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addRow("التصنيف:", self.category_combo)
        
        # مبلغ الميزانية
        self.budget_amount = QDoubleSpinBox()
        self.budget_amount.setRange(0, 999999)
        self.budget_amount.setSuffix(" ريال")
        self.budget_amount.setDecimals(2)
        form_layout.addRow("مبلغ الميزانية:", self.budget_amount)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ الميزانية")
        save_btn.setObjectName("primary_button")
        save_btn.clicked.connect(self.save_budget)
        buttons_layout.addWidget(save_btn)
        
        clear_btn = QPushButton("مسح")
        clear_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول الميزانيات الحالية
        self.budget_table = QTableWidget()
        self.budget_table.setColumnCount(3)
        self.budget_table.setHorizontalHeaderLabels(["التصنيف", "الميزانية", "إجراءات"])
        self.budget_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.budget_table)
        
        return group
        
    def create_budget_display_panel(self):
        """إنشاء لوحة عرض الميزانيات"""
        group = QGroupBox("حالة الميزانيات")
        layout = QVBoxLayout(group)
        
        # منطقة التمرير للبطاقات
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.cards_widget = QWidget()
        self.cards_layout = QVBoxLayout(self.cards_widget)
        self.cards_layout.setSpacing(10)
        
        scroll.setWidget(self.cards_widget)
        layout.addWidget(scroll)
        
        # الرسم البياني
        self.create_budget_chart(layout)
        
        return group
        
    def create_budget_chart(self, parent_layout):
        """إنشاء الرسم البياني للميزانيات"""
        chart_frame = QFrame()
        chart_frame.setMaximumHeight(300)
        chart_layout = QVBoxLayout(chart_frame)
        
        # إنشاء الرسم البياني
        self.figure = Figure(figsize=(10, 4))
        self.canvas = FigureCanvas(self.figure)
        chart_layout.addWidget(self.canvas)
        
        parent_layout.addWidget(chart_frame)
        
    def load_categories(self):
        """تحميل التصنيفات"""
        categories = self.db.get_categories()
        self.category_combo.clear()
        for category in categories:
            self.category_combo.addItem(category['name'])
            
    def on_date_changed(self):
        """عند تغيير التاريخ"""
        self.current_month = self.month_combo.currentIndex() + 1
        self.current_year = self.year_spin.value()
        self.load_budget_data()
        
    def save_budget(self):
        """حفظ الميزانية"""
        category = self.category_combo.currentText()
        amount = self.budget_amount.value()
        
        if not category or amount <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى ملء جميع الحقول بقيم صحيحة")
            return
            
        success = self.db.set_monthly_budget(category, self.current_year, self.current_month, amount)
        
        if success:
            QMessageBox.information(self, "نجح", "تم حفظ الميزانية بنجاح")
            self.clear_form()
            self.load_budget_data()
            self.budget_updated.emit()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في حفظ الميزانية")
            
    def clear_form(self):
        """مسح النموذج"""
        self.budget_amount.setValue(0)
        
    def load_budget_data(self):
        """تحميل بيانات الميزانيات"""
        self.load_budget_table()
        self.load_budget_cards()
        self.update_budget_chart()
        
    def load_budget_table(self):
        """تحميل جدول الميزانيات"""
        budgets = self.db.get_all_monthly_budgets(self.current_year, self.current_month)
        
        self.budget_table.setRowCount(len(budgets))
        
        for row, budget in enumerate(budgets):
            # التصنيف
            self.budget_table.setItem(row, 0, QTableWidgetItem(budget['category']))
            
            # الميزانية
            amount_item = QTableWidgetItem(f"{budget['budget']:.2f} ريال")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.budget_table.setItem(row, 1, amount_item)
            
            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.setObjectName("danger_button")
            delete_btn.clicked.connect(lambda checked, cat=budget['category']: self.delete_budget(cat))
            self.budget_table.setCellWidget(row, 2, delete_btn)
            
    def load_budget_cards(self):
        """تحميل بطاقات الميزانيات"""
        # مسح البطاقات الموجودة
        for i in reversed(range(self.cards_layout.count())):
            child = self.cards_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
            
        # جلب بيانات المقارنة
        budget_data = self.db.get_budget_vs_spending(self.current_year, self.current_month)
        
        if not budget_data:
            no_data_label = QLabel("لا توجد ميزانيات محددة لهذا الشهر")
            no_data_label.setAlignment(Qt.AlignCenter)
            no_data_label.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 20px;")
            self.cards_layout.addWidget(no_data_label)
            return
            
        for item in budget_data:
            card = self.create_budget_card(item)
            self.cards_layout.addWidget(card)
            
        self.cards_layout.addStretch()
        
    def create_budget_card(self, budget_item):
        """إنشاء بطاقة ميزانية"""
        card = QFrame()
        card.setObjectName("budget_card")
        card.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(10)
        
        # عنوان التصنيف
        title = QLabel(budget_item['category'])
        title.setObjectName("card_title")
        layout.addWidget(title)
        
        # شريط التقدم
        progress = QProgressBar()
        progress.setMaximum(100)
        progress.setValue(min(int(budget_item['percentage']), 100))
        
        # تلوين شريط التقدم حسب الحالة
        if budget_item['status'] == 'over':
            progress.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
        elif budget_item['status'] == 'warning':
            progress.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
        else:
            progress.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")
            
        layout.addWidget(progress)
        
        # تفاصيل الميزانية
        details_layout = QGridLayout()
        
        details_layout.addWidget(QLabel("الميزانية:"), 0, 0)
        details_layout.addWidget(QLabel(f"{budget_item['budget']:.2f} ريال"), 0, 1)
        
        details_layout.addWidget(QLabel("المُنفق:"), 1, 0)
        details_layout.addWidget(QLabel(f"{budget_item['spent']:.2f} ريال"), 1, 1)
        
        details_layout.addWidget(QLabel("المتبقي:"), 2, 0)
        remaining_label = QLabel(f"{budget_item['remaining']:.2f} ريال")
        if budget_item['remaining'] < 0:
            remaining_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        else:
            remaining_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        details_layout.addWidget(remaining_label, 2, 1)
        
        layout.addLayout(details_layout)
        
        # نسبة الإنفاق
        percentage_label = QLabel(f"{budget_item['percentage']:.1f}%")
        percentage_label.setAlignment(Qt.AlignCenter)
        percentage_label.setObjectName("percentage_label")
        layout.addWidget(percentage_label)
        
        return card
        
    def update_budget_chart(self):
        """تحديث الرسم البياني"""
        self.figure.clear()
        
        budget_data = self.db.get_budget_vs_spending(self.current_year, self.current_month)
        
        if not budget_data:
            return
            
        ax = self.figure.add_subplot(111)
        
        categories = [item['category'] for item in budget_data]
        budgets = [item['budget'] for item in budget_data]
        spent = [item['spent'] for item in budget_data]
        
        x = range(len(categories))
        width = 0.35
        
        bars1 = ax.bar([i - width/2 for i in x], budgets, width, label='الميزانية', color='#3498db', alpha=0.8)
        bars2 = ax.bar([i + width/2 for i in x], spent, width, label='المُنفق', alpha=0.8)
        
        # تلوين أعمدة المُنفق حسب الحالة
        for i, item in enumerate(budget_data):
            if item['status'] == 'over':
                bars2[i].set_color('#e74c3c')
            elif item['status'] == 'warning':
                bars2[i].set_color('#f39c12')
            else:
                bars2[i].set_color('#27ae60')
        
        ax.set_xlabel('التصنيفات')
        ax.set_ylabel('المبلغ (ريال)')
        ax.set_title(f'مقارنة الميزانية والإنفاق - {self.month_combo.currentText()} {self.current_year}')
        ax.set_xticks(x)
        ax.set_xticklabels(categories, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        self.figure.tight_layout()
        self.canvas.draw()
        
    def delete_budget(self, category):
        """حذف ميزانية"""
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل أنت متأكد من حذف ميزانية {category}؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # حذف من قاعدة البيانات
            try:
                with self.db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        DELETE FROM monthly_budgets 
                        WHERE category = ? AND year = ? AND month = ?
                    ''', (category, self.current_year, self.current_month))
                    conn.commit()
                    
                QMessageBox.information(self, "تم الحذف", "تم حذف الميزانية بنجاح")
                self.load_budget_data()
                self.budget_updated.emit()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الميزانية: {str(e)}")
