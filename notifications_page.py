import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QGroupBox,
                             QMessageBox, QCheckBox, QSpinBox, QComboBox,
                             QFormLayout, QSizePolicy, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from responsive_utils import ResponsiveUtils
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon

class NotificationCard(QFrame):
    """بطاقة تنبيه واحد"""
    
    notification_read = pyqtSignal(int)
    notification_deleted = pyqtSignal(int)
    
    def __init__(self, notification_data):
        super().__init__()
        self.notification = notification_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة البطاقة"""
        self.setObjectName("notification_card")
        self.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # رأس التنبيه
        header_layout = QHBoxLayout()
        
        # أيقونة التنبيه حسب النوع
        icon_label = QLabel(self.get_notification_icon())
        icon_label.setObjectName("notification_icon")
        header_layout.addWidget(icon_label)
        
        # عنوان التنبيه
        title_label = QLabel(self.notification['title'])
        title_label.setObjectName("notification_title")
        title_label.setWordWrap(True)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # وقت التنبيه
        time_label = QLabel(self.format_time())
        time_label.setObjectName("notification_time")
        header_layout.addWidget(time_label)
        
        layout.addLayout(header_layout)
        
        # محتوى التنبيه
        message_label = QLabel(self.notification['message'])
        message_label.setObjectName("notification_message")
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        if not self.notification['is_read']:
            mark_read_btn = QPushButton("تحديد كمقروء")
            mark_read_btn.setObjectName("primary_button")
            mark_read_btn.clicked.connect(self.mark_as_read)
            buttons_layout.addWidget(mark_read_btn)
        
        delete_btn = QPushButton("حذف")
        delete_btn.setObjectName("danger_button")
        delete_btn.clicked.connect(self.delete_notification)
        buttons_layout.addWidget(delete_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تطبيق الستايل حسب حالة القراءة
        if not self.notification['is_read']:
            self.setStyleSheet("""
                #notification_card {
                    border-left: 4px solid #3498db;
                    background-color: #f8f9fa;
                }
            """)
        else:
            self.setStyleSheet("""
                #notification_card {
                    border-left: 4px solid #bdc3c7;
                    background-color: #ffffff;
                }
            """)
            
    def get_notification_icon(self):
        """الحصول على أيقونة التنبيه حسب النوع"""
        icons = {
            'budget_exceeded': '🚨',
            'budget_warning': '⚠️',
            'goal_achieved': '🎉',
            'goal_progress': '📈',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }
        return icons.get(self.notification['type'], 'ℹ️')
        
    def format_time(self):
        """تنسيق وقت التنبيه"""
        try:
            created_time = datetime.fromisoformat(self.notification['created_at'].replace('Z', '+00:00'))
            now = datetime.now()
            diff = now - created_time
            
            if diff.days > 0:
                return f"منذ {diff.days} يوم"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"منذ {hours} ساعة"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"منذ {minutes} دقيقة"
            else:
                return "الآن"
        except:
            return "غير محدد"
            
    def mark_as_read(self):
        """تحديد التنبيه كمقروء"""
        self.notification_read.emit(self.notification['id'])
        
    def delete_notification(self):
        """حذف التنبيه"""
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل أنت متأكد من حذف هذا التنبيه؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.notification_deleted.emit(self.notification['id'])

class NotificationsPage(QWidget):
    """صفحة التنبيهات"""
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.auto_check_timer = QTimer()
        self.auto_check_timer.timeout.connect(self.check_automatic_notifications)
        self.auto_check_timer.start(60000)  # فحص كل دقيقة
        self.init_ui()
        self.load_notifications()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الصفحة
        title = QLabel("التنبيهات والإشعارات")
        title.setObjectName("page_title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        main_content = QHBoxLayout()
        
        # الجانب الأيسر - قائمة التنبيهات
        left_panel = self.create_notifications_panel()
        main_content.addWidget(left_panel, 2)
        
        # الجانب الأيمن - إعدادات التنبيهات
        right_panel = self.create_settings_panel()
        main_content.addWidget(right_panel, 1)
        
        layout.addLayout(main_content)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setObjectName("toolbar")
        layout = QHBoxLayout(toolbar)
        
        # عداد التنبيهات غير المقروءة
        unread_count = len(self.db.get_notifications(unread_only=True))
        self.unread_label = QLabel(f"التنبيهات غير المقروءة: {unread_count}")
        self.unread_label.setObjectName("unread_counter")
        layout.addWidget(self.unread_label)
        
        layout.addStretch()
        
        # زر تحديد الكل كمقروء
        mark_all_btn = QPushButton("تحديد الكل كمقروء")
        mark_all_btn.clicked.connect(self.mark_all_as_read)
        layout.addWidget(mark_all_btn)
        
        # زر حذف المقروءة
        delete_read_btn = QPushButton("حذف المقروءة")
        delete_read_btn.setObjectName("danger_button")
        delete_read_btn.clicked.connect(self.delete_read_notifications)
        layout.addWidget(delete_read_btn)
        
        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.load_notifications)
        layout.addWidget(refresh_btn)
        
        return toolbar
        
    def create_notifications_panel(self):
        """إنشاء لوحة التنبيهات"""
        group = QGroupBox("التنبيهات الحديثة")
        layout = QVBoxLayout(group)
        
        # فلاتر التنبيهات
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("عرض:"))
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["جميع التنبيهات", "غير المقروءة فقط", "المقروءة فقط"])
        self.filter_combo.currentTextChanged.connect(self.load_notifications)
        filter_layout.addWidget(self.filter_combo)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # منطقة التمرير للتنبيهات
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.notifications_widget = QWidget()
        self.notifications_layout = QVBoxLayout(self.notifications_widget)
        self.notifications_layout.setSpacing(10)
        
        scroll.setWidget(self.notifications_widget)
        layout.addWidget(scroll)
        
        return group
        
    def create_settings_panel(self):
        """إنشاء لوحة إعدادات التنبيهات"""
        group = QGroupBox("إعدادات التنبيهات")
        layout = QVBoxLayout(group)
        
        # إعدادات التنبيهات التلقائية
        auto_settings = QGroupBox("التنبيهات التلقائية")
        auto_layout = QFormLayout(auto_settings)
        
        # تنبيهات الميزانية
        self.budget_alerts_check = QCheckBox("تنبيهات تجاوز الميزانية")
        self.budget_alerts_check.setChecked(True)
        auto_layout.addRow(self.budget_alerts_check)
        
        # حد التنبيه للميزانية
        self.budget_threshold = QSpinBox()
        self.budget_threshold.setRange(50, 100)
        self.budget_threshold.setValue(80)
        self.budget_threshold.setSuffix("%")
        auto_layout.addRow("تنبيه عند الوصول إلى:", self.budget_threshold)
        
        # تنبيهات الأهداف
        self.goal_alerts_check = QCheckBox("تنبيهات تحقيق الأهداف")
        self.goal_alerts_check.setChecked(True)
        auto_layout.addRow(self.goal_alerts_check)
        
        layout.addWidget(auto_settings)
        
        # أزرار التحكم
        buttons_layout = QVBoxLayout()
        
        # زر فحص التنبيهات الآن
        check_now_btn = QPushButton("فحص التنبيهات الآن")
        check_now_btn.setObjectName("primary_button")
        check_now_btn.clicked.connect(self.check_automatic_notifications)
        buttons_layout.addWidget(check_now_btn)
        
        # زر إنشاء تنبيه تجريبي
        test_btn = QPushButton("تنبيه تجريبي")
        test_btn.clicked.connect(self.create_test_notification)
        buttons_layout.addWidget(test_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return group
        
    def load_notifications(self):
        """تحميل التنبيهات"""
        # مسح التنبيهات الموجودة
        for i in reversed(range(self.notifications_layout.count())):
            child = self.notifications_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        # تحديد نوع الفلتر
        filter_text = self.filter_combo.currentText()
        if filter_text == "غير المقروءة فقط":
            notifications = self.db.get_notifications(unread_only=True)
        elif filter_text == "المقروءة فقط":
            all_notifications = self.db.get_notifications()
            notifications = [n for n in all_notifications if n['is_read']]
        else:
            notifications = self.db.get_notifications()
            
        if not notifications:
            no_notifications_label = QLabel("لا توجد تنبيهات")
            no_notifications_label.setAlignment(Qt.AlignCenter)
            no_notifications_label.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 40px;")
            self.notifications_layout.addWidget(no_notifications_label)
        else:
            for notification in notifications:
                card = NotificationCard(notification)
                card.notification_read.connect(self.mark_notification_read)
                card.notification_deleted.connect(self.delete_notification)
                self.notifications_layout.addWidget(card)
                
        self.notifications_layout.addStretch()
        
        # تحديث عداد غير المقروءة
        unread_count = len(self.db.get_notifications(unread_only=True))
        self.unread_label.setText(f"التنبيهات غير المقروءة: {unread_count}")
        
    def mark_notification_read(self, notification_id):
        """تحديد تنبيه كمقروء"""
        success = self.db.mark_notification_read(notification_id)
        if success:
            self.load_notifications()
            
    def delete_notification(self, notification_id):
        """حذف تنبيه"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM notifications WHERE id = ?', (notification_id,))
                conn.commit()
                
            self.load_notifications()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف التنبيه: {str(e)}")
            
    def mark_all_as_read(self):
        """تحديد جميع التنبيهات كمقروءة"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('UPDATE notifications SET is_read = 1 WHERE is_read = 0')
                conn.commit()
                
            QMessageBox.information(self, "تم", "تم تحديد جميع التنبيهات كمقروءة")
            self.load_notifications()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث التنبيهات: {str(e)}")
            
    def delete_read_notifications(self):
        """حذف التنبيهات المقروءة"""
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   "هل أنت متأكد من حذف جميع التنبيهات المقروءة؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                with self.db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('DELETE FROM notifications WHERE is_read = 1')
                    conn.commit()
                    
                QMessageBox.information(self, "تم الحذف", "تم حذف التنبيهات المقروءة")
                self.load_notifications()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف التنبيهات: {str(e)}")
                
    def check_automatic_notifications(self):
        """فحص التنبيهات التلقائية"""
        current_date = datetime.now()
        
        # فحص تنبيهات الميزانية
        if self.budget_alerts_check.isChecked():
            self.check_budget_notifications(current_date.year, current_date.month)
            
        # فحص تنبيهات الأهداف
        if self.goal_alerts_check.isChecked():
            self.check_goal_notifications()
            
    def check_budget_notifications(self, year, month):
        """فحص تنبيهات الميزانية"""
        alerts = self.db.check_budget_alerts(year, month)
        threshold = self.budget_threshold.value()
        
        for alert in alerts:
            # تجنب التنبيهات المكررة
            existing = self.db.get_notifications()
            alert_exists = any(
                n['type'] == alert['type'] and 
                alert['category'] in n['message'] and
                not n['is_read']
                for n in existing
            )
            
            if not alert_exists:
                if alert['type'] == 'budget_exceeded':
                    self.db.add_notification(
                        "🚨 تجاوز الميزانية!",
                        alert['message'],
                        "budget_exceeded"
                    )
                elif alert['type'] == 'budget_warning':
                    # فقط إذا تجاوز الحد المحدد
                    budget_data = self.db.get_budget_vs_spending(year, month)
                    for item in budget_data:
                        if item['category'] == alert['category'] and item['percentage'] >= threshold:
                            self.db.add_notification(
                                "⚠️ تحذير الميزانية",
                                alert['message'],
                                "budget_warning"
                            )
                            break
                            
    def check_goal_notifications(self):
        """فحص تنبيهات الأهداف"""
        goals = self.db.get_financial_goals()
        
        for goal in goals:
            if goal['progress_percentage'] >= 100:
                # تحقق من عدم وجود تنبيه مسبق لهذا الهدف
                existing = self.db.get_notifications()
                goal_notified = any(
                    n['type'] == 'goal_achieved' and 
                    goal['name'] in n['message']
                    for n in existing
                )
                
                if not goal_notified:
                    self.db.add_notification(
                        "🎉 تهانينا!",
                        f"لقد حققت هدفك: {goal['name']}",
                        "goal_achieved"
                    )
                    
    def create_test_notification(self):

    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            # تطبيق تحسينات أساسية
            ResponsiveUtils.apply_responsive_font(self, 14, size_category)
            ResponsiveUtils.apply_responsive_margins(self, 15, size_category)
            
            # تحسينات خاصة بالهاتف
            if size_category in ['xs', 'sm']:
                ResponsiveUtils.optimize_for_mobile(self, size_category)
                
        except Exception as e:
            print(f"خطأ في تحديث التخطيط: {e}")

        """إنشاء تنبيه تجريبي"""
        self.db.add_notification(
            "📝 تنبيه تجريبي",
            "هذا تنبيه تجريبي للتأكد من عمل النظام بشكل صحيح",
            "info"
        )
        self.load_notifications()
        QMessageBox.information(self, "تم", "تم إنشاء تنبيه تجريبي")
