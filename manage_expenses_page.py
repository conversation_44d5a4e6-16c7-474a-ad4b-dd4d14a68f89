from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QTableWidget, QTableWidgetItem, QPushButton,
                             QFrame, QHeaderView, QMessageBox, QDialog,
                             QLineEdit, QComboBox, QTextEdit, QDateEdit,
                             QDoubleSpinBox, QFormLayout, QDialogButtonBox,
                             QAbstractItemView, QMenu, QAction)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from responsive_utils import ResponsiveUtils
from PyQt5.QtGui import QFont, QContextMenuEvent

class EditExpenseDialog(QDialog):
    """نافذة تعديل المصروف"""
    
    def __init__(self, expense_data, categories, parent=None):
        super().__init__(parent)
        self.expense_data = expense_data
        self.categories = categories
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تعديل المصروف")
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title = QLabel("✏️ تعديل المصروف")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # النموذج
        form_frame = QFrame()
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اسم المصروف
        self.name_input = QLineEdit()
        form_layout.addRow("اسم المصروف:", self.name_input)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        form_layout.addRow("المبلغ:", self.amount_input)
        
        # التصنيف
        self.category_combo = QComboBox()
        for category in self.categories:
            self.category_combo.addItem(category['name'])
        form_layout.addRow("التصنيف:", self.category_combo)
        
        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDisplayFormat("yyyy-MM-dd")
        form_layout.addRow("التاريخ:", self.date_input)
        
        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        form_layout.addRow("الملاحظات:", self.notes_input)
        
        layout.addWidget(form_frame)
        
        # الأزرار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("حفظ")
        button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def load_data(self):
        """تحميل بيانات المصروف"""
        self.name_input.setText(self.expense_data['name'])
        self.amount_input.setValue(self.expense_data['amount'])
        
        # تعيين التصنيف
        category_index = self.category_combo.findText(self.expense_data['category'])
        if category_index >= 0:
            self.category_combo.setCurrentIndex(category_index)
            
        # تعيين التاريخ
        date = QDate.fromString(self.expense_data['date'], "yyyy-MM-dd")
        self.date_input.setDate(date)
        
        # تعيين الملاحظات
        self.notes_input.setPlainText(self.expense_data.get('notes', ''))
        
    def get_data(self):
        """الحصول على البيانات المحدثة"""
        return {
            'id': self.expense_data['id'],
            'name': self.name_input.text().strip(),
            'amount': self.amount_input.value(),
            'category': self.category_combo.currentText(),
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText().strip()
        }


class ManageExpensesPage(QWidget):
    """صفحة إدارة المصاريف"""
    
    # إشارة لإعلام النافذة الرئيسية بتحديث المصاريف
    expense_updated = pyqtSignal()
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.expenses_data = []
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان الرئيسي
        title = QLabel("📝 إدارة المصاريف")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignRight)
        layout.addWidget(title)
        
        # أزرار العمليات
        self.create_action_buttons(layout)
        
        # جدول المصاريف
        self.create_expenses_table(layout)
        
    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار العمليات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        
        # زر التعديل
        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setObjectName("warning_btn")
        self.edit_btn.clicked.connect(self.edit_expense)
        self.edit_btn.setEnabled(False)
        
        # زر الحذف
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setObjectName("danger_btn")
        self.delete_btn.clicked.connect(self.delete_expense)
        self.delete_btn.setEnabled(False)
        
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        
        parent_layout.addWidget(buttons_frame)
        
    def create_expenses_table(self, parent_layout):
        """إنشاء جدول المصاريف"""
        table_frame = QFrame()
        table_frame.setObjectName("card")
        table_layout = QVBoxLayout(table_frame)
        
        # عنوان الجدول
        table_title = QLabel("قائمة المصاريف")
        table_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        table_title.setAlignment(Qt.AlignRight)
        table_layout.addWidget(table_title)
        
        # الجدول
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "المبلغ", "التصنيف", "التاريخ", "الملاحظات"
        ])
        
        # إعدادات الجدول
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الرقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # التصنيف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # التاريخ
        
        # ربط إشارة تحديد الصف
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
        # ربط النقر المزدوج للتعديل
        self.table.doubleClicked.connect(self.edit_expense)
        
        table_layout.addWidget(self.table)
        parent_layout.addWidget(table_frame)
        
    def refresh_data(self):
        """تحديث بيانات الجدول"""
        # جلب البيانات من قاعدة البيانات
        self.expenses_data = self.db.get_expenses()
        
        # تحديث الجدول
        self.table.setRowCount(len(self.expenses_data))
        
        for row, expense in enumerate(self.expenses_data):
            # الرقم
            id_item = QTableWidgetItem(str(expense['id']))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 0, id_item)
            
            # الاسم
            name_item = QTableWidgetItem(expense['name'])
            name_item.setTextAlignment(Qt.AlignRight)
            self.table.setItem(row, 1, name_item)
            
            # المبلغ
            amount_item = QTableWidgetItem(f"{expense['amount']:,.2f} ريال")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, amount_item)
            
            # التصنيف
            category_item = QTableWidgetItem(expense['category'])
            category_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 3, category_item)
            
            # التاريخ
            date_item = QTableWidgetItem(expense['date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 4, date_item)
            
            # الملاحظات
            notes = expense.get('notes', '')
            notes_item = QTableWidgetItem(notes[:50] + "..." if len(notes) > 50 else notes)
            notes_item.setTextAlignment(Qt.AlignRight)
            self.table.setItem(row, 5, notes_item)
        
        # إعادة تعيين حالة الأزرار
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)

    def on_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""
        selected_rows = self.table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def get_selected_expense(self):
        """الحصول على المصروف المحدد"""
        selected_rows = self.table.selectionModel().selectedRows()
        if not selected_rows:
            return None

        row = selected_rows[0].row()
        if row < len(self.expenses_data):
            return self.expenses_data[row]
        return None

    def edit_expense(self):
        """تعديل المصروف المحدد"""
        expense = self.get_selected_expense()
        if not expense:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للتعديل")
            return

        # جلب التصنيفات
        categories = self.db.get_categories()

        # فتح نافذة التعديل
        dialog = EditExpenseDialog(expense, categories, self)
        if dialog.exec_() == QDialog.Accepted:
            # الحصول على البيانات المحدثة
            updated_data = dialog.get_data()

            # التحقق من صحة البيانات
            if not updated_data['name'].strip():
                QMessageBox.warning(self, "خطأ", "يجب إدخال اسم المصروف")
                return

            if updated_data['amount'] <= 0:
                QMessageBox.warning(self, "خطأ", "يجب إدخال مبلغ أكبر من صفر")
                return

            # تحديث قاعدة البيانات
            success = self.db.update_expense(
                updated_data['id'],
                updated_data['name'],
                updated_data['amount'],
                updated_data['category'],
                updated_data['date'],
                updated_data['notes']
            )

            if success:
                QMessageBox.information(self, "نجح التحديث", "تم تحديث المصروف بنجاح!")
                self.refresh_data()
                self.expense_updated.emit()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث المصروف!")

    def delete_expense(self):
        """حذف المصروف المحدد"""
        expense = self.get_selected_expense()
        if not expense:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحذف")
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المصروف '{expense['name']}'؟\n"
            f"المبلغ: {expense['amount']:,.2f} ريال\n"
            f"التاريخ: {expense['date']}\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف من قاعدة البيانات
            success = self.db.delete_expense(expense['id'])

            if success:
                QMessageBox.information(self, "نجح الحذف", "تم حذف المصروف بنجاح!")
                self.refresh_data()
                self.expense_updated.emit()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف المصروف!")

    def contextMenuEvent(self, event):
        """قائمة السياق (النقر بالزر الأيمن)"""
        if self.table.itemAt(event.pos()):
            menu = QMenu(self)

            edit_action = QAction("✏️ تعديل", self)
            edit_action.triggered.connect(self.edit_expense)
            menu.addAction(edit_action)

            delete_action = QAction("🗑️ حذف", self)
            delete_action.triggered.connect(self.delete_expense)
            menu.addAction(delete_action)

            menu.exec_(event.globalPos())

    def keyPressEvent(self, event):

    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            # تطبيق تحسينات أساسية
            ResponsiveUtils.apply_responsive_font(self, 14, size_category)
            ResponsiveUtils.apply_responsive_margins(self, 15, size_category)
            
            # تحسينات خاصة بالهاتف
            if size_category in ['xs', 'sm']:
                ResponsiveUtils.optimize_for_mobile(self, size_category)
                
        except Exception as e:
            print(f"خطأ في تحديث التخطيط: {e}")

        """معالجة ضغط المفاتيح"""
        # تعديل بالضغط على Enter أو F2
        if event.key() in (Qt.Key_Return, Qt.Key_Enter, Qt.Key_F2):
            if self.edit_btn.isEnabled():
                self.edit_expense()
        # حذف بالضغط على Delete
        elif event.key() == Qt.Key_Delete:
            if self.delete_btn.isEnabled():
                self.delete_expense()
        # تحديث بالضغط على F5
        elif event.key() == Qt.Key_F5:
            self.refresh_data()
        else:
            super().keyPressEvent(event)
