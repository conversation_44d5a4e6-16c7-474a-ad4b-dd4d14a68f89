#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التصميم المتجاوب المتقدم
يدير أحجام الشاشات والتكيف التلقائي للواجهة
"""

from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QScreen
import sys

class ResponsiveManager(QObject):
    """مدير التصميم المتجاوب"""
    
    # إشارات التغيير
    size_changed = pyqtSignal(str)  # xs, sm, md, lg, xl
    orientation_changed = pyqtSignal(str)  # portrait, landscape
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.current_size = "lg"
        self.current_orientation = "landscape"
        self.is_touch_device = self.detect_touch_device()
        
        # نقاط التوقف للأحجام
        self.breakpoints = {
            'xs': 480,    # هاتف صغير
            'sm': 768,    # هاتف كبير
            'md': 1024,   # تابلت
            'lg': 1440,   # سطح مكتب
            'xl': 1920    # شاشة كبيرة
        }
        
        # إعداد مراقب تغيير الحجم
        self.setup_resize_monitor()
        
        # تطبيق الحجم الأولي
        self.update_responsive_size()
    
    def detect_touch_device(self):
        """كشف الأجهزة اللمسية"""
        try:
            # فحص إذا كان النظام يدعم اللمس
            app = QApplication.instance()
            if app:
                screens = app.screens()
                for screen in screens:
                    # فحص DPI للشاشة
                    dpi = screen.logicalDotsPerInch()
                    if dpi > 150:  # شاشات عالية الدقة عادة لمسية
                        return True
            return False
        except:
            return False
    
    def setup_resize_monitor(self):
        """إعداد مراقب تغيير حجم النافذة"""
        self.main_window.resizeEvent = self.on_window_resize
        
        # مؤقت لتأخير التحديث
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self.update_responsive_size)
    
    def on_window_resize(self, event):
        """معالج تغيير حجم النافذة"""
        # تأخير التحديث لتحسين الأداء
        self.resize_timer.start(100)
        
        # استدعاء المعالج الأصلي إذا وجد
        if hasattr(self.main_window, '_original_resize_event'):
            self.main_window._original_resize_event(event)
    
    def get_current_size_category(self):
        """الحصول على فئة الحجم الحالية"""
        width = self.main_window.width()
        
        if width <= self.breakpoints['xs']:
            return 'xs'
        elif width <= self.breakpoints['sm']:
            return 'sm'
        elif width <= self.breakpoints['md']:
            return 'md'
        elif width <= self.breakpoints['lg']:
            return 'lg'
        else:
            return 'xl'
    
    def get_current_orientation(self):
        """الحصول على اتجاه الشاشة الحالي"""
        width = self.main_window.width()
        height = self.main_window.height()
        
        return 'portrait' if height > width else 'landscape'
    
    def update_responsive_size(self):
        """تحديث الحجم المتجاوب"""
        new_size = self.get_current_size_category()
        new_orientation = self.get_current_orientation()
        
        # تحديث الحجم إذا تغير
        if new_size != self.current_size:
            self.current_size = new_size
            self.apply_responsive_styles(new_size)
            self.size_changed.emit(new_size)
        
        # تحديث الاتجاه إذا تغير
        if new_orientation != self.current_orientation:
            self.current_orientation = new_orientation
            self.orientation_changed.emit(new_orientation)
        
        # تطبيق تحسينات خاصة للأجهزة اللمسية
        if self.is_touch_device:
            self.apply_touch_optimizations()
    
    def apply_responsive_styles(self, size_category):
        """تطبيق الأنماط المتجاوبة"""
        # تعيين خاصية الحجم للنافذة الرئيسية
        self.main_window.setProperty("responsive-size", size_category)
        
        # تطبيق الأنماط على جميع الويدجت الفرعية
        self.apply_to_children(self.main_window, size_category)
        
        # إعادة تطبيق الأنماط
        self.main_window.style().unpolish(self.main_window)
        self.main_window.style().polish(self.main_window)
        
        # تحديث تخطيط الشريط الجانبي
        self.update_sidebar_layout(size_category)
        
        # تحديث تخطيط البطاقات
        self.update_cards_layout(size_category)
    
    def apply_to_children(self, widget, size_category):
        """تطبيق الأنماط على الويدجت الفرعية"""
        widget.setProperty("responsive-size", size_category)
        
        for child in widget.findChildren(QWidget):
            child.setProperty("responsive-size", size_category)
            child.style().unpolish(child)
            child.style().polish(child)
    
    def apply_touch_optimizations(self):
        """تطبيق تحسينات الأجهزة اللمسية"""
        # تعيين خصائص اللمس
        self.main_window.setProperty("touch-optimized", "true")
        
        # تحسين أشرطة التمرير
        scroll_areas = self.main_window.findChildren(QWidget, "QScrollArea")
        for scroll_area in scroll_areas:
            scroll_area.setProperty("touch-optimized", "true")
        
        # تحسين الجداول للهاتف
        if self.current_size in ['xs', 'sm']:
            tables = self.main_window.findChildren(QWidget, "QTableWidget")
            for table in tables:
                table.setProperty("mobile-view", "true")
    
    def update_sidebar_layout(self, size_category):
        """تحديث تخطيط الشريط الجانبي"""
        try:
            sidebar = self.main_window.findChild(QWidget, "sidebar")
            if sidebar:
                if size_category in ['xs', 'sm']:
                    # وضع الهاتف - شريط جانبي قابل للطي
                    sidebar.setProperty("mobile-mode", "true")
                    sidebar.setFixedWidth(0)  # إخفاء مؤقت
                else:
                    # وضع سطح المكتب - شريط جانبي ثابت
                    sidebar.setProperty("mobile-mode", "false")
                    sidebar.setFixedWidth(250)
        except:
            pass
    
    def update_cards_layout(self, size_category):
        """تحديث تخطيط البطاقات"""
        try:
            # تحديث شبكة البطاقات حسب حجم الشاشة
            cards_layouts = self.main_window.findChildren(QWidget)
            
            for widget in cards_layouts:
                if hasattr(widget, 'stats_layout'):
                    layout = widget.stats_layout
                    
                    # إعادة ترتيب البطاقات حسب الحجم
                    if size_category == 'xs':
                        # عمود واحد للهواتف الصغيرة
                        self.rearrange_grid(layout, 1)
                    elif size_category == 'sm':
                        # عمودين للهواتف الكبيرة
                        self.rearrange_grid(layout, 2)
                    elif size_category == 'md':
                        # ثلاثة أعمدة للتابلت
                        self.rearrange_grid(layout, 3)
                    else:
                        # ثلاثة أعمدة أو أكثر لسطح المكتب
                        self.rearrange_grid(layout, 3)
        except:
            pass
    
    def rearrange_grid(self, layout, columns):
        """إعادة ترتيب الشبكة"""
        try:
            if hasattr(layout, 'count'):
                items = []
                # جمع جميع العناصر
                for i in range(layout.count()):
                    item = layout.itemAt(i)
                    if item and item.widget():
                        items.append(item.widget())
                
                # إعادة ترتيب العناصر
                for i, widget in enumerate(items):
                    row = i // columns
                    col = i % columns
                    layout.addWidget(widget, row, col)
        except:
            pass
    
    def get_optimal_font_size(self, base_size=14):
        """الحصول على حجم الخط الأمثل"""
        multipliers = {
            'xs': 0.85,
            'sm': 0.9,
            'md': 1.0,
            'lg': 1.1,
            'xl': 1.2
        }
        
        return int(base_size * multipliers.get(self.current_size, 1.0))
    
    def is_mobile_size(self):
        """فحص إذا كان الحجم الحالي للهاتف"""
        return self.current_size in ['xs', 'sm']
    
    def is_desktop_size(self):
        """فحص إذا كان الحجم الحالي لسطح المكتب"""
        return self.current_size in ['lg', 'xl']
    
    def get_responsive_padding(self, base_padding=20):
        """الحصول على المسافة المتجاوبة"""
        multipliers = {
            'xs': 0.6,
            'sm': 0.8,
            'md': 1.0,
            'lg': 1.2,
            'xl': 1.4
        }
        
        return int(base_padding * multipliers.get(self.current_size, 1.0))
    
    def get_responsive_margin(self, base_margin=10):
        """الحصول على الهامش المتجاوب"""
        multipliers = {
            'xs': 0.4,
            'sm': 0.6,
            'md': 1.0,
            'lg': 1.2,
            'xl': 1.5
        }
        
        return int(base_margin * multipliers.get(self.current_size, 1.0))
