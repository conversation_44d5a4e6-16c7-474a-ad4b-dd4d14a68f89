import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                             QPushButton, QStackedWidget, QFrame, QApplication,
                             QLabel, QSizePolicy)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette

# استيراد مدير التصميم المتجاوب
from responsive_manager import ResponsiveManager
from layout_manager import LayoutManager
from responsive_grid import ResponsiveContainer, ResponsiveGridWidget

# استيراد الصفحات
from dashboard_page import DashboardPage
from add_expense_page import AddExpensePage
from manage_expenses_page import ManageExpensesPage
from budget_page import BudgetPage
from goals_page import GoalsPage
from notifications_page import NotificationsPage
from ai_insights_page import AIInsightsPage
from database import ExpenseDatabase
from pdf_exporter import PDFExporter

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.db = ExpenseDatabase()
        self.pdf_exporter = PDFExporter(self.db)
        self.init_ui()
        self.load_styles()

        # إنشاء مدير التصميم المتجاوب
        self.responsive_manager = ResponsiveManager(self)

        # إنشاء مدير التخطيطات
        self.layout_manager = LayoutManager(self)

        # ربط إشارات التجاوب
        self.responsive_manager.size_changed.connect(self.on_responsive_size_changed)
        self.responsive_manager.orientation_changed.connect(self.on_orientation_changed)
        self.responsive_manager.size_changed.connect(self.layout_manager.update_layouts)
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المصاريف الشخصية")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 700)
        
        # تعيين اتجاه النص من اليمين لليسار
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الشريط الجانبي
        self.create_sidebar()
        
        # إنشاء منطقة المحتوى الرئيسي
        self.create_main_content()
        
        # إضافة الشريط الجانبي والمحتوى للتخطيط
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.main_content, 1)
        
        # تعيين الصفحة الافتراضية
        self.show_dashboard()
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي للتنقل"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # شعار التطبيق
        logo_label = QLabel("💰 إدارة المصاريف")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 30px 20px;
                background-color: #2c3e50;
                border-bottom: 2px solid #34495e;
            }
        """)
        sidebar_layout.addWidget(logo_label)
        
        # أزرار التنقل
        self.nav_buttons = []
        
        # زر لوحة المعلومات
        dashboard_btn = QPushButton("📊 لوحة المعلومات")
        dashboard_btn.setCheckable(True)
        dashboard_btn.setChecked(True)
        dashboard_btn.clicked.connect(self.show_dashboard)
        self.nav_buttons.append(dashboard_btn)
        
        # زر إضافة مصروف
        add_expense_btn = QPushButton("➕ إضافة مصروف")
        add_expense_btn.setCheckable(True)
        add_expense_btn.clicked.connect(self.show_add_expense)
        self.nav_buttons.append(add_expense_btn)
        
        # زر إدارة المصاريف
        manage_expenses_btn = QPushButton("📝 إدارة المصاريف")
        manage_expenses_btn.setCheckable(True)
        manage_expenses_btn.clicked.connect(self.show_manage_expenses)
        self.nav_buttons.append(manage_expenses_btn)

        # زر الميزانيات
        budget_btn = QPushButton("💰 الميزانيات")
        budget_btn.setCheckable(True)
        budget_btn.clicked.connect(self.show_budget)
        self.nav_buttons.append(budget_btn)

        # زر الأهداف المالية
        goals_btn = QPushButton("🎯 الأهداف المالية")
        goals_btn.setCheckable(True)
        goals_btn.clicked.connect(self.show_goals)
        self.nav_buttons.append(goals_btn)

        # زر التنبيهات
        notifications_btn = QPushButton("🔔 التنبيهات")
        notifications_btn.setCheckable(True)
        notifications_btn.clicked.connect(self.show_notifications)
        self.nav_buttons.append(notifications_btn)

        # زر المساعد الذكي
        ai_btn = QPushButton("🤖 المساعد الذكي")
        ai_btn.setCheckable(True)
        ai_btn.clicked.connect(self.show_ai_insights)
        self.nav_buttons.append(ai_btn)

        # زر التقارير
        reports_btn = QPushButton("📄 التقارير")
        reports_btn.setCheckable(True)
        reports_btn.clicked.connect(self.show_reports)
        self.nav_buttons.append(reports_btn)
        
        # إضافة الأزرار للتخطيط
        for btn in self.nav_buttons:
            btn.setObjectName("nav_button")
            sidebar_layout.addWidget(btn)
        
        # إضافة مساحة فارغة في الأسفل
        sidebar_layout.addStretch()
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
                padding: 20px;
                background-color: transparent;
            }
        """)
        sidebar_layout.addWidget(version_label)
        
    def create_main_content(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        self.main_content = QFrame()
        self.main_content.setObjectName("main_content")
        
        content_layout = QVBoxLayout(self.main_content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء الصفحات المكدسة
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)
        
        # إنشاء الصفحات
        self.dashboard_page = DashboardPage(self.db)
        self.add_expense_page = AddExpensePage(self.db)
        self.manage_expenses_page = ManageExpensesPage(self.db)
        self.budget_page = BudgetPage(self.db)
        self.goals_page = GoalsPage(self.db)
        self.notifications_page = NotificationsPage(self.db)
        self.ai_insights_page = AIInsightsPage(self.db)

        # إضافة الصفحات للمكدس
        self.stacked_widget.addWidget(self.dashboard_page)
        self.stacked_widget.addWidget(self.add_expense_page)
        self.stacked_widget.addWidget(self.manage_expenses_page)
        self.stacked_widget.addWidget(self.budget_page)
        self.stacked_widget.addWidget(self.goals_page)
        self.stacked_widget.addWidget(self.notifications_page)
        self.stacked_widget.addWidget(self.ai_insights_page)
        
        # ربط إشارات تحديث البيانات
        self.add_expense_page.expense_added.connect(self.refresh_data)
        self.manage_expenses_page.expense_updated.connect(self.refresh_data)
        self.budget_page.budget_updated.connect(self.refresh_data)
        self.goals_page.goal_updated.connect(self.refresh_data)
        
    def load_styles(self):
        """تحميل ملف الأنماط"""
        try:
            with open("styles.qss", "r", encoding="utf-8") as f:
                self.setStyleSheet(f.read())
        except FileNotFoundError:
            print("ملف الأنماط غير موجود")
    
    def show_dashboard(self):
        """عرض صفحة لوحة المعلومات"""
        self.stacked_widget.setCurrentWidget(self.dashboard_page)
        self.dashboard_page.refresh_data()
        self.update_nav_buttons(0)
        
    def show_add_expense(self):
        """عرض صفحة إضافة مصروف"""
        self.stacked_widget.setCurrentWidget(self.add_expense_page)
        self.add_expense_page.clear_form()
        self.update_nav_buttons(1)
        
    def show_manage_expenses(self):
        """عرض صفحة إدارة المصاريف"""
        self.stacked_widget.setCurrentWidget(self.manage_expenses_page)
        self.manage_expenses_page.refresh_data()
        self.update_nav_buttons(2)
        
    def show_reports(self):
        """عرض صفحة التقارير"""
        from PyQt5.QtWidgets import QMessageBox, QInputDialog
        from datetime import datetime

        # اختيار الشهر والسنة
        current_year = datetime.now().year
        current_month = datetime.now().month

        # قائمة الأشهر
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]

        # اختيار الشهر
        month_name, ok1 = QInputDialog.getItem(
            self, "اختيار الشهر", "اختر الشهر:", months, current_month - 1, False
        )

        if not ok1:
            return

        month_number = months.index(month_name) + 1

        # اختيار السنة
        year, ok2 = QInputDialog.getInt(
            self, "اختيار السنة", "اختر السنة:", current_year, 2020, 2030, 1
        )

        if not ok2:
            return

        try:
            # إنشاء التقرير
            filename = self.pdf_exporter.create_monthly_report(year, month_number)
            QMessageBox.information(
                self, "نجح التصدير",
                f"تم إنشاء التقرير بنجاح!\nاسم الملف: {filename}"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "خطأ",
                f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}"
            )
        
    def show_budget(self):
        """عرض صفحة الميزانيات"""
        self.stacked_widget.setCurrentWidget(self.budget_page)
        self.budget_page.load_budget_data()
        self.update_nav_buttons(3)

    def show_goals(self):
        """عرض صفحة الأهداف المالية"""
        self.stacked_widget.setCurrentWidget(self.goals_page)
        self.goals_page.load_goals()
        self.update_nav_buttons(4)

    def show_notifications(self):
        """عرض صفحة التنبيهات"""
        self.stacked_widget.setCurrentWidget(self.notifications_page)
        self.notifications_page.load_notifications()
        self.update_nav_buttons(5)

    def show_ai_insights(self):
        """عرض صفحة المساعد الذكي"""
        self.stacked_widget.setCurrentWidget(self.ai_insights_page)
        self.ai_insights_page.load_insights()
        self.update_nav_buttons(6)

    def update_nav_buttons(self, active_index):
        """تحديث حالة أزرار التنقل"""
        for i, btn in enumerate(self.nav_buttons):
            btn.setChecked(i == active_index)
    
    def refresh_data(self):
        """تحديث البيانات في جميع الصفحات"""
        self.dashboard_page.refresh_data()
        self.manage_expenses_page.refresh_data()
    
    def on_responsive_size_changed(self, size_category):
        """معالج تغيير حجم الشاشة"""
        print(f"تم تغيير حجم الشاشة إلى: {size_category}")

        # تحديث تخطيط الشريط الجانبي
        if hasattr(self, 'sidebar'):
            if size_category in ['xs', 'sm']:
                # وضع الهاتف - إخفاء الشريط الجانبي
                self.sidebar.setVisible(False)
            else:
                # وضع سطح المكتب - إظهار الشريط الجانبي
                self.sidebar.setVisible(True)

        # تحديث جميع الصفحات
        self.update_pages_responsive_layout(size_category)

    def on_orientation_changed(self, orientation):
        """معالج تغيير اتجاه الشاشة"""
        print(f"تم تغيير اتجاه الشاشة إلى: {orientation}")

        # تطبيق تحسينات خاصة بالاتجاه
        if orientation == 'portrait':
            # وضع عمودي - تحسينات للهاتف
            self.apply_portrait_optimizations()
        else:
            # وضع أفقي - تحسينات للتابلت/سطح المكتب
            self.apply_landscape_optimizations()

    def update_pages_responsive_layout(self, size_category):
        """تحديث تخطيط الصفحات للتجاوب"""
        try:
            # تحديث صفحة لوحة المعلومات
            if hasattr(self, 'dashboard_page'):
                self.dashboard_page.update_responsive_layout(size_category)

            # تحديث صفحة الميزانيات
            if hasattr(self, 'budget_page'):
                self.budget_page.update_responsive_layout(size_category)

            # تحديث صفحة الأهداف
            if hasattr(self, 'goals_page'):
                self.goals_page.update_responsive_layout(size_category)

        except Exception as e:
            print(f"خطأ في تحديث التخطيط المتجاوب: {e}")

    def apply_portrait_optimizations(self):
        """تطبيق تحسينات الوضع العمودي"""
        try:
            # تحسينات خاصة بالوضع العمودي
            if hasattr(self, 'main_content'):
                self.main_content.setContentsMargins(8, 8, 8, 8)
        except:
            pass

    def apply_landscape_optimizations(self):
        """تطبيق تحسينات الوضع الأفقي"""
        try:
            # تحسينات خاصة بالوضع الأفقي
            if hasattr(self, 'main_content'):
                self.main_content.setContentsMargins(16, 16, 16, 16)
        except:
            pass

    def toggle_sidebar(self):
        """تبديل إظهار/إخفاء الشريط الجانبي"""
        if hasattr(self, 'sidebar'):
            self.sidebar.setVisible(not self.sidebar.isVisible())

    def closeEvent(self, event):
        """معالجة إغلاق التطبيق"""
        event.accept()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # تعيين الخط الافتراضي للعربية
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # تعيين اتجاه التطبيق من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
