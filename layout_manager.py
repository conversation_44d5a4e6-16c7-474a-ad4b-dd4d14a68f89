#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التخطيطات المتجاوبة
يدير التخطيطات المختلفة للصفحات حسب حجم الشاشة
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QStackedLayout
from PyQt5.QtWidgets import QFrame, QScrollArea, QSplitter
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from responsive_grid import ResponsiveGridWidget, ResponsiveContainer, ResponsiveRow

class LayoutManager(QObject):
    """مدير التخطيطات المتجاوبة"""
    
    layout_changed = pyqtSignal(str, str)  # size_category, layout_type
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_size = 'lg'
        self.layouts = {}
        self.active_widgets = []
        
    def register_widget(self, widget, layout_configs):
        """تسجيل ويدجت مع إعدادات التخطيط"""
        self.active_widgets.append({
            'widget': widget,
            'configs': layout_configs
        })
        
    def update_layouts(self, size_category):
        """تحديث جميع التخطيطات للحجم الجديد"""
        if size_category != self.current_size:
            self.current_size = size_category
            
            for widget_data in self.active_widgets:
                widget = widget_data['widget']
                configs = widget_data['configs']
                
                if size_category in configs:
                    config = configs[size_category]
                    self.apply_layout_config(widget, config)
                    
            self.layout_changed.emit(size_category, 'updated')
    
    def apply_layout_config(self, widget, config):
        """تطبيق إعدادات التخطيط على ويدجت"""
        try:
            layout_type = config.get('type', 'vertical')
            
            if layout_type == 'vertical':
                self.apply_vertical_layout(widget, config)
            elif layout_type == 'horizontal':
                self.apply_horizontal_layout(widget, config)
            elif layout_type == 'grid':
                self.apply_grid_layout(widget, config)
            elif layout_type == 'stacked':
                self.apply_stacked_layout(widget, config)
                
        except Exception as e:
            print(f"خطأ في تطبيق التخطيط: {e}")
    
    def apply_vertical_layout(self, widget, config):
        """تطبيق تخطيط عمودي"""
        if hasattr(widget, 'layout') and widget.layout():
            # تحويل التخطيط الحالي إلى عمودي
            self.convert_to_vertical(widget)
            
        # تطبيق إعدادات إضافية
        spacing = config.get('spacing', 10)
        margins = config.get('margins', [10, 10, 10, 10])
        
        if widget.layout():
            widget.layout().setSpacing(spacing)
            widget.layout().setContentsMargins(*margins)
    
    def apply_horizontal_layout(self, widget, config):
        """تطبيق تخطيط أفقي"""
        if hasattr(widget, 'layout') and widget.layout():
            # تحويل التخطيط الحالي إلى أفقي
            self.convert_to_horizontal(widget)
            
        # تطبيق إعدادات إضافية
        spacing = config.get('spacing', 10)
        margins = config.get('margins', [10, 10, 10, 10])
        
        if widget.layout():
            widget.layout().setSpacing(spacing)
            widget.layout().setContentsMargins(*margins)
    
    def apply_grid_layout(self, widget, config):
        """تطبيق تخطيط شبكة"""
        columns = config.get('columns', 3)
        spacing = config.get('spacing', 10)
        margins = config.get('margins', [10, 10, 10, 10])
        
        # إنشاء تخطيط شبكة جديد
        if hasattr(widget, 'update_grid_columns'):
            widget.update_grid_columns(columns)
        else:
            self.convert_to_grid(widget, columns)
            
        if widget.layout():
            widget.layout().setSpacing(spacing)
            widget.layout().setContentsMargins(*margins)
    
    def apply_stacked_layout(self, widget, config):
        """تطبيق تخطيط مكدس"""
        # تحويل إلى تخطيط مكدس للهواتف
        self.convert_to_stacked(widget)
    
    def convert_to_vertical(self, widget):
        """تحويل التخطيط إلى عمودي"""
        try:
            current_layout = widget.layout()
            if not current_layout or isinstance(current_layout, QVBoxLayout):
                return
                
            # جمع جميع العناصر
            items = []
            while current_layout.count():
                item = current_layout.takeAt(0)
                if item.widget():
                    items.append(item.widget())
                    
            # إنشاء تخطيط عمودي جديد
            new_layout = QVBoxLayout(widget)
            
            # إضافة العناصر عمودياً
            for item_widget in items:
                new_layout.addWidget(item_widget)
                
        except Exception as e:
            print(f"خطأ في التحويل إلى عمودي: {e}")
    
    def convert_to_horizontal(self, widget):
        """تحويل التخطيط إلى أفقي"""
        try:
            current_layout = widget.layout()
            if not current_layout or isinstance(current_layout, QHBoxLayout):
                return
                
            # جمع جميع العناصر
            items = []
            while current_layout.count():
                item = current_layout.takeAt(0)
                if item.widget():
                    items.append(item.widget())
                    
            # إنشاء تخطيط أفقي جديد
            new_layout = QHBoxLayout(widget)
            
            # إضافة العناصر أفقياً
            for item_widget in items:
                new_layout.addWidget(item_widget)
                
        except Exception as e:
            print(f"خطأ في التحويل إلى أفقي: {e}")
    
    def convert_to_grid(self, widget, columns):
        """تحويل التخطيط إلى شبكة"""
        try:
            current_layout = widget.layout()
            if not current_layout:
                return
                
            # جمع جميع العناصر
            items = []
            while current_layout.count():
                item = current_layout.takeAt(0)
                if item.widget():
                    items.append(item.widget())
                    
            # إنشاء تخطيط شبكة جديد
            new_layout = QGridLayout(widget)
            
            # إضافة العناصر في شبكة
            for i, item_widget in enumerate(items):
                row = i // columns
                col = i % columns
                new_layout.addWidget(item_widget, row, col)
                
        except Exception as e:
            print(f"خطأ في التحويل إلى شبكة: {e}")
    
    def convert_to_stacked(self, widget):
        """تحويل التخطيط إلى مكدس"""
        try:
            current_layout = widget.layout()
            if not current_layout or isinstance(current_layout, QStackedLayout):
                return
                
            # جمع جميع العناصر
            items = []
            while current_layout.count():
                item = current_layout.takeAt(0)
                if item.widget():
                    items.append(item.widget())
                    
            # إنشاء تخطيط مكدس جديد
            new_layout = QStackedLayout(widget)
            
            # إضافة العناصر للمكدس
            for item_widget in items:
                new_layout.addWidget(item_widget)
                
            # عرض العنصر الأول
            if items:
                new_layout.setCurrentWidget(items[0])
                
        except Exception as e:
            print(f"خطأ في التحويل إلى مكدس: {e}")

class ResponsivePageLayout(QFrame):
    """تخطيط صفحة متجاوب"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.sections = []
        self.current_layout_type = 'desktop'
        self.setup_layout()
        
    def setup_layout(self):
        """إعداد التخطيط الأساسي"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # إنشاء منطقة التمرير
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # إنشاء ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        
        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area)
        
    def add_section(self, widget, mobile_config=None):
        """إضافة قسم للصفحة"""
        section_data = {
            'widget': widget,
            'mobile_config': mobile_config or {}
        }
        
        self.sections.append(section_data)
        self.content_layout.addWidget(widget)
        
    def switch_to_mobile_layout(self):
        """التبديل إلى تخطيط الهاتف"""
        if self.current_layout_type == 'mobile':
            return
            
        self.current_layout_type = 'mobile'
        
        # تطبيق تحسينات الهاتف
        for section_data in self.sections:
            widget = section_data['widget']
            mobile_config = section_data['mobile_config']
            
            # تطبيق إعدادات الهاتف
            if 'hide' in mobile_config and mobile_config['hide']:
                widget.setVisible(False)
            elif 'full_width' in mobile_config and mobile_config['full_width']:
                widget.setMaximumWidth(16777215)
                
    def switch_to_desktop_layout(self):
        """التبديل إلى تخطيط سطح المكتب"""
        if self.current_layout_type == 'desktop':
            return
            
        self.current_layout_type = 'desktop'
        
        # تطبيق إعدادات سطح المكتب
        for section_data in self.sections:
            widget = section_data['widget']
            
            # إظهار جميع العناصر
            widget.setVisible(True)
            
            # تطبيق عرض محدود
            widget.setMaximumWidth(1200)

class AdaptiveLayout(QFrame):
    """تخطيط تكيفي يغير نفسه حسب المحتوى"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.items = []
        self.min_item_width = 200
        self.setup_layout()
        
    def setup_layout(self):
        """إعداد التخطيط"""
        self.current_layout = QHBoxLayout(self)
        
    def add_item(self, widget, min_width=None):
        """إضافة عنصر للتخطيط التكيفي"""
        if min_width:
            widget.setMinimumWidth(min_width)
            
        self.items.append(widget)
        self.current_layout.addWidget(widget)
        
    def resizeEvent(self, event):
        """معالج تغيير الحجم"""
        super().resizeEvent(event)
        self.adapt_layout()
        
    def adapt_layout(self):
        """تكييف التخطيط حسب المساحة المتاحة"""
        available_width = self.width()
        items_count = len(self.items)
        
        if items_count == 0:
            return
            
        # حساب العرض المطلوب لكل عنصر
        required_width = items_count * self.min_item_width
        
        if available_width < required_width:
            # التبديل إلى تخطيط عمودي
            self.switch_to_vertical()
        else:
            # التبديل إلى تخطيط أفقي
            self.switch_to_horizontal()
            
    def switch_to_vertical(self):
        """التبديل إلى تخطيط عمودي"""
        if isinstance(self.current_layout, QVBoxLayout):
            return
            
        # حفظ العناصر
        items = []
        while self.current_layout.count():
            item = self.current_layout.takeAt(0)
            if item.widget():
                items.append(item.widget())
                
        # إنشاء تخطيط عمودي جديد
        self.current_layout.deleteLater()
        self.current_layout = QVBoxLayout(self)
        
        # إعادة إضافة العناصر
        for widget in items:
            self.current_layout.addWidget(widget)
            
    def switch_to_horizontal(self):
        """التبديل إلى تخطيط أفقي"""
        if isinstance(self.current_layout, QHBoxLayout):
            return
            
        # حفظ العناصر
        items = []
        while self.current_layout.count():
            item = self.current_layout.takeAt(0)
            if item.widget():
                items.append(item.widget())
                
        # إنشاء تخطيط أفقي جديد
        self.current_layout.deleteLater()
        self.current_layout = QHBoxLayout(self)
        
        # إعادة إضافة العناصر
        for widget in items:
            self.current_layout.addWidget(widget)
