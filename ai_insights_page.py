import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QScrollArea, QGroupBox,
                             QTextEdit, QComboBox, QSpinBox, QFormLayout,
                             QSizePolicy, QGridLayout, QProgressBar, QLineEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from responsive_utils import ResponsiveUtils
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap, QPainter
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from ai_assistant import AIAssistant

class InsightCard(QFrame):
    """بطاقة نصيحة أو تحليل"""
    
    def __init__(self, title, content, icon="💡", color="#3498db"):
        super().__init__()
        self.init_ui(title, content, icon, color)
        
    def init_ui(self, title, content, icon, color):
        """إعداد واجهة البطاقة"""
        self.setObjectName("insight_card")
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            #insight_card {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }}
            #insight_card:hover {{
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # رأس البطاقة
        header_layout = QHBoxLayout()
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setObjectName("insight_title")
        title_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {color};")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # المحتوى
        content_label = QLabel(content)
        content_label.setObjectName("insight_content")
        content_label.setWordWrap(True)
        content_label.setStyleSheet("font-size: 14px; color: #2c3e50; line-height: 1.4;")
        layout.addWidget(content_label)

class AIInsightsPage(QWidget):
    """صفحة التحليلات الذكية والنصائح الشخصية"""
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.ai_assistant = AIAssistant(database)
        self.init_ui()
        self.load_insights()
        
        # تحديث تلقائي كل 5 دقائق
        self.auto_update_timer = QTimer()
        self.auto_update_timer.timeout.connect(self.load_insights)
        self.auto_update_timer.start(300000)  # 5 دقائق
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الصفحة
        title = QLabel("🤖 المساعد الذكي والتحليلات")
        title.setObjectName("page_title")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # المحتوى الرئيسي
        main_content = QHBoxLayout()
        
        # الجانب الأيسر - النصائح والتحليلات
        left_panel = self.create_insights_panel()
        main_content.addWidget(left_panel, 2)
        
        # الجانب الأيمن - أدوات التحليل
        right_panel = self.create_tools_panel()
        main_content.addWidget(right_panel, 1)
        
        layout.addLayout(main_content)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QFrame()
        toolbar.setObjectName("toolbar")
        layout = QHBoxLayout(toolbar)
        
        # معلومات آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: الآن")
        self.last_update_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        layout.addWidget(self.last_update_label)
        
        layout.addStretch()
        
        # زر تحديث التحليلات
        refresh_btn = QPushButton("تحديث التحليلات")
        refresh_btn.setObjectName("primary_button")
        refresh_btn.clicked.connect(self.load_insights)
        layout.addWidget(refresh_btn)
        
        # زر التصنيف التلقائي
        auto_categorize_btn = QPushButton("تصنيف تلقائي للمصاريف")
        auto_categorize_btn.clicked.connect(self.auto_categorize_expenses)
        layout.addWidget(auto_categorize_btn)
        
        return toolbar
        
    def create_insights_panel(self):
        """إنشاء لوحة النصائح والتحليلات"""
        group = QGroupBox("التحليلات الذكية والنصائح الشخصية")
        layout = QVBoxLayout(group)
        
        # منطقة التمرير للنصائح
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.insights_widget = QWidget()
        self.insights_layout = QVBoxLayout(self.insights_widget)
        self.insights_layout.setSpacing(15)
        
        scroll.setWidget(self.insights_widget)
        layout.addWidget(scroll)
        
        return group
        
    def create_tools_panel(self):
        """إنشاء لوحة أدوات التحليل"""
        group = QGroupBox("أدوات التحليل الذكي")
        layout = QVBoxLayout(group)
        
        # أداة التوقع الشهري
        prediction_group = QGroupBox("توقع الإنفاق الشهري")
        prediction_layout = QVBoxLayout(prediction_group)
        
        self.prediction_label = QLabel("جاري حساب التوقع...")
        self.prediction_label.setWordWrap(True)
        prediction_layout.addWidget(self.prediction_label)
        
        self.prediction_progress = QProgressBar()
        self.prediction_progress.setVisible(False)
        prediction_layout.addWidget(self.prediction_progress)
        
        layout.addWidget(prediction_group)
        
        # أداة تحليل التصنيفات
        category_group = QGroupBox("تحليل التصنيفات")
        category_layout = QFormLayout(category_group)
        
        self.category_combo = QComboBox()
        self.load_categories()
        category_layout.addRow("اختر التصنيف:", self.category_combo)
        
        self.analysis_days = QSpinBox()
        self.analysis_days.setRange(7, 365)
        self.analysis_days.setValue(30)
        self.analysis_days.setSuffix(" يوم")
        category_layout.addRow("فترة التحليل:", self.analysis_days)
        
        analyze_btn = QPushButton("تحليل التصنيف")
        analyze_btn.clicked.connect(self.analyze_category)
        category_layout.addWidget(analyze_btn)
        
        self.category_analysis_label = QLabel("اختر تصنيفاً للتحليل")
        self.category_analysis_label.setWordWrap(True)
        self.category_analysis_label.setStyleSheet("margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        category_layout.addWidget(self.category_analysis_label)
        
        layout.addWidget(category_group)
        
        # أداة كشف الإنفاق غير العادي
        unusual_group = QGroupBox("كشف الإنفاق غير العادي")
        unusual_layout = QVBoxLayout(unusual_group)
        
        detect_btn = QPushButton("فحص الإنفاق غير العادي")
        detect_btn.clicked.connect(self.detect_unusual_spending)
        unusual_layout.addWidget(detect_btn)
        
        self.unusual_label = QLabel("لم يتم الفحص بعد")
        self.unusual_label.setWordWrap(True)
        self.unusual_label.setStyleSheet("margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        unusual_layout.addWidget(self.unusual_label)
        
        layout.addWidget(unusual_group)
        
        layout.addStretch()
        
        return group
        
    def load_categories(self):
        """تحميل التصنيفات"""
        categories = self.db.get_categories()
        self.category_combo.clear()
        for category in categories:
            self.category_combo.addItem(category['name'])
            
    def load_insights(self):
        """تحميل التحليلات والنصائح"""
        # مسح التحليلات الموجودة
        for i in reversed(range(self.insights_layout.count())):
            child = self.insights_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # تحديث وقت آخر تحديث
        self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%H:%M')}")
        
        # جلب النصائح الشخصية
        tips = self.ai_assistant.generate_personalized_tips()
        
        if tips:
            for i, tip in enumerate(tips):
                icon = ["💡", "📊", "🎯", "⚠️", "✨"][i % 5]
                color = ["#3498db", "#27ae60", "#f39c12", "#e74c3c", "#9b59b6"][i % 5]
                card = InsightCard(f"نصيحة {i+1}", tip, icon, color)
                self.insights_layout.addWidget(card)
        
        # تحليل أنماط الإنفاق
        patterns = self.ai_assistant.analyze_spending_patterns()
        
        if patterns:
            # بطاقة المتوسط اليومي
            daily_avg = patterns.get('daily_average', 0)
            avg_card = InsightCard(
                "متوسط الإنفاق اليومي",
                f"متوسط إنفاقك اليومي هو {daily_avg:.2f} ريال خلال آخر 30 يوماً",
                "📈",
                "#3498db"
            )
            self.insights_layout.addWidget(avg_card)
            
            # بطاقة اتجاه الإنفاق
            trend = patterns.get('spending_trend', 'stable')
            trend_text = {
                'increasing': 'إنفاقك في ازدياد مقارنة بالأسبوع الماضي',
                'decreasing': 'إنفاقك في تحسن مقارنة بالأسبوع الماضي',
                'stable': 'إنفاقك مستقر مقارنة بالأسبوع الماضي'
            }
            trend_icon = {
                'increasing': '📈',
                'decreasing': '📉',
                'stable': '➡️'
            }
            trend_color = {
                'increasing': '#e74c3c',
                'decreasing': '#27ae60',
                'stable': '#f39c12'
            }
            
            trend_card = InsightCard(
                "اتجاه الإنفاق",
                trend_text.get(trend, 'غير محدد'),
                trend_icon.get(trend, '➡️'),
                trend_color.get(trend, '#f39c12')
            )
            self.insights_layout.addWidget(trend_card)
            
            # بطاقة التصنيف الأكثر إنفاقاً
            top_category = patterns.get('most_expensive_category', '')
            if top_category:
                category_card = InsightCard(
                    "التصنيف الأكثر إنفاقاً",
                    f"تنفق أكثر في تصنيف: {top_category}",
                    "🏆",
                    "#9b59b6"
                )
                self.insights_layout.addWidget(category_card)
        
        # تحديث توقع الإنفاق الشهري
        self.update_monthly_prediction()
        
        self.insights_layout.addStretch()
        
    def update_monthly_prediction(self):
        """تحديث توقع الإنفاق الشهري"""
        prediction = self.ai_assistant.predict_monthly_spending()
        
        if prediction:
            current = prediction.get('current_spending', 0)
            predicted_total = prediction.get('predicted_total', 0)
            confidence = prediction.get('confidence', 'low')
            message = prediction.get('message', '')
            days_passed = prediction.get('days_passed', 0)
            days_remaining = prediction.get('days_remaining', 0)
            
            confidence_text = {
                'high': 'عالية',
                'medium': 'متوسطة',
                'low': 'منخفضة'
            }
            
            prediction_text = f"""
            💰 الإنفاق الحالي: {current:.2f} ريال
            📊 التوقع للشهر: {predicted_total:.2f} ريال
            📅 الأيام المنقضية: {days_passed} يوم
            ⏳ الأيام المتبقية: {days_remaining} يوم
            🎯 دقة التوقع: {confidence_text.get(confidence, 'غير محدد')}
            
            📝 {message}
            """
            
            self.prediction_label.setText(prediction_text.strip())
            
            # إظهار شريط التقدم للشهر
            if days_passed > 0:
                month_progress = (days_passed / (days_passed + days_remaining)) * 100
                self.prediction_progress.setValue(int(month_progress))
                self.prediction_progress.setVisible(True)
                self.prediction_progress.setFormat(f"تقدم الشهر: {month_progress:.1f}%")
        
    def analyze_category(self):
        """تحليل تصنيف محدد"""
        category = self.category_combo.currentText()
        days = self.analysis_days.value()
        
        if not category:
            return
            
        insights = self.ai_assistant.get_category_insights(category, days)
        
        if 'message' in insights:
            self.category_analysis_label.setText(insights['message'])
            return
        
        total = insights.get('total_amount', 0)
        average = insights.get('average_amount', 0)
        frequency = insights.get('frequency', 0)
        tips = insights.get('tips', [])
        
        analysis_text = f"""
        📊 تحليل تصنيف: {category}
        
        💰 إجمالي الإنفاق: {total:.2f} ريال
        📈 متوسط المصروف: {average:.2f} ريال
        🔄 التكرار: {frequency:.1f} مرة/يوم
        
        """
        
        if tips:
            analysis_text += "💡 نصائح:\n"
            for tip in tips:
                analysis_text += f"• {tip}\n"
        
        self.category_analysis_label.setText(analysis_text.strip())
        
    def detect_unusual_spending(self):
        """كشف الإنفاق غير العادي"""
        unusual_expenses = self.ai_assistant.detect_unusual_spending()
        
        if not unusual_expenses:
            self.unusual_label.setText("✅ لم يتم اكتشاف إنفاق غير عادي في آخر 30 يوماً")
            return
        
        unusual_text = f"⚠️ تم اكتشاف {len(unusual_expenses)} مصروف غير عادي:\n\n"
        
        for unusual in unusual_expenses[:5]:  # أظهر أقصى 5
            expense = unusual['expense']
            severity = unusual['severity']
            deviation = unusual['deviation']
            
            severity_icon = "🔴" if severity == 'high' else "🟡"
            unusual_text += f"{severity_icon} {expense['name']}: {expense['amount']:.2f} ريال\n"
            unusual_text += f"   الانحراف: +{deviation:.2f} ريال عن المتوسط\n\n"
        
        self.unusual_label.setText(unusual_text.strip())
        
    def auto_categorize_expenses(self):

    def update_responsive_layout(self, size_category):
        """تحديث التخطيط للتجاوب"""
        try:
            # تطبيق تحسينات أساسية
            ResponsiveUtils.apply_responsive_font(self, 14, size_category)
            ResponsiveUtils.apply_responsive_margins(self, 15, size_category)
            
            # تحسينات خاصة بالهاتف
            if size_category in ['xs', 'sm']:
                ResponsiveUtils.optimize_for_mobile(self, size_category)
                
        except Exception as e:
            print(f"خطأ في تحديث التخطيط: {e}")

        """تصنيف تلقائي للمصاريف غير المصنفة"""
        # جلب المصاريف غير المصنفة أو المصنفة كـ "أخرى"
        all_expenses = self.db.get_expenses()
        uncategorized = [exp for exp in all_expenses if exp['category'] == 'أخرى']
        
        if not uncategorized:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "تصنيف تلقائي", "جميع المصاريف مصنفة بالفعل!")
            return
        
        categorized_count = 0
        
        for expense in uncategorized:
            suggested_category = self.ai_assistant.auto_categorize_expense(
                expense['name'], 
                expense['amount']
            )
            
            if suggested_category != 'أخرى':
                # تحديث التصنيف في قاعدة البيانات
                success = self.db.update_expense(
                    expense['id'],
                    expense['name'],
                    expense['amount'],
                    suggested_category,
                    expense['date'],
                    expense['notes']
                )
                
                if success:
                    categorized_count += 1
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(
            self, 
            "تصنيف تلقائي", 
            f"تم تصنيف {categorized_count} مصروف تلقائياً من أصل {len(uncategorized)}"
        )
        
        # تحديث التحليلات
        self.load_insights()
