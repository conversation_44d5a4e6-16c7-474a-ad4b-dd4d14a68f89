# 📖 دليل المستخدم السريع - برنامج إدارة المصاريف الشخصية

## 🚀 البدء السريع

### تشغيل التطبيق
```bash
python app.py
```

### أول استخدام
1. سيتم إنشاء قاعدة البيانات تلقائياً
2. ستظهر التصنيفات الافتراضية
3. يمكنك البدء فوراً بإضافة المصاريف

## 🏠 لوحة المعلومات

### البطاقات الإحصائية
- **إجمالي المصاريف**: مجموع جميع مصاريفك
- **عدد المصاريف**: عدد العمليات المسجلة
- **متوسط المصروف**: متوسط قيمة المصروف الواحد
- **أكبر مصروف**: أعلى مصروف مسجل
- **الميزانيات**: عدد الميزانيات المتجاوزة
- **الأهداف المحققة**: عدد الأهداف المكتملة

### الرسوم البيانية
- **الرسم الدائري**: توزيع المصاريف حسب التصنيف
- **الرسم الخطي**: اتجاه الإنفاق عبر الوقت
- **فلاتر التاريخ**: اختر الفترة الزمنية للعرض

## ➕ إضافة مصروف جديد

### الحقول المطلوبة
1. **اسم المصروف**: وصف واضح للمصروف
2. **المبلغ**: قيمة المصروف بالريال
3. **التصنيف**: اختر من القائمة أو استخدم التصنيف التلقائي
4. **التاريخ**: تاريخ المصروف (افتراضياً اليوم)
5. **ملاحظات**: تفاصيل إضافية (اختياري)

### ميزة التصنيف التلقائي
- اكتب اسم المصروف أولاً
- اضغط "تصنيف تلقائي"
- سيقترح النظام التصنيف المناسب تلقائياً

### اختصارات لوحة المفاتيح
- **Ctrl+S**: حفظ المصروف
- **Ctrl+R**: مسح النموذج

## 📝 إدارة المصاريف

### عرض المصاريف
- جدول شامل لجميع المصاريف
- فلترة حسب التاريخ والتصنيف
- بحث سريع في أسماء المصاريف

### تعديل المصاريف
1. اضغط "تعديل" بجانب المصروف
2. عدّل البيانات في النافذة المنبثقة
3. احفظ التغييرات

### حذف المصاريف
- اضغط "حذف" بجانب المصروف
- أكد الحذف في النافذة المنبثقة

## 💰 إدارة الميزانيات

### إنشاء ميزانية شهرية
1. اختر الشهر والسنة
2. حدد التصنيف
3. أدخل مبلغ الميزانية
4. احفظ الميزانية

### مراقبة الميزانيات
- **بطاقات الميزانية**: عرض حالة كل ميزانية
- **أشرطة التقدم**: نسبة الإنفاق من الميزانية
- **الألوان التحذيرية**:
  - 🟢 أخضر: إنفاق آمن (أقل من 80%)
  - 🟡 أصفر: تحذير (80-100%)
  - 🔴 أحمر: تجاوز الميزانية

### الرسم البياني المقارن
- مقارنة الميزانية مع الإنفاق الفعلي
- عرض بصري لجميع التصنيفات

## 🎯 الأهداف المالية

### إنشاء هدف جديد
1. اضغط "إضافة هدف جديد"
2. أدخل اسم الهدف
3. حدد المبلغ المطلوب
4. اختر التاريخ المستهدف
5. أضف وصفاً (اختياري)

### تتبع التقدم
- **شريط التقدم**: نسبة التقدم نحو الهدف
- **المبلغ المحقق**: ما تم ادخاره حتى الآن
- **المبلغ المتبقي**: ما يجب ادخاره لتحقيق الهدف

### إضافة مبالغ للأهداف
1. اضغط "إضافة مبلغ" في بطاقة الهدف
2. أدخل المبلغ المراد إضافته
3. سيتم تحديث التقدم تلقائياً

### إحصائيات الأهداف
- **إجمالي الأهداف**: عدد الأهداف المسجلة
- **الأهداف المكتملة**: عدد الأهداف المحققة
- **إجمالي المبلغ المطلوب**: مجموع جميع الأهداف
- **إجمالي المبلغ المحقق**: ما تم ادخاره فعلياً

## 🔔 التنبيهات والإشعارات

### أنواع التنبيهات
- **تجاوز الميزانية**: عند تجاوز حد الميزانية
- **تحذير الميزانية**: عند الاقتراب من الحد (80%)
- **تحقيق الأهداف**: عند إكمال هدف مالي
- **تنبيهات مخصصة**: يمكن إنشاؤها يدوياً

### إدارة التنبيهات
- **فلترة**: عرض جميع التنبيهات أو غير المقروءة فقط
- **تحديد كمقروء**: تحديد تنبيه واحد أو جميع التنبيهات
- **حذف**: حذف تنبيه واحد أو جميع المقروءة

### إعدادات التنبيهات
- **تنبيهات الميزانية**: تفعيل/إلغاء تنبيهات الميزانية
- **حد التنبيه**: تحديد النسبة للتحذير (افتراضياً 80%)
- **تنبيهات الأهداف**: تفعيل/إلغاء تنبيهات الأهداف

## 🤖 المساعد الذكي

### النصائح الشخصية
- نصائح مخصصة بناءً على عادات الإنفاق
- تحليل أنماط الإنفاق
- اقتراحات للتوفير والتحسين

### التصنيف التلقائي
- **في صفحة إضافة المصاريف**: زر "تصنيف تلقائي"
- **للمصاريف الموجودة**: زر "تصنيف تلقائي للمصاريف"
- يعتمد على الكلمات المفتاحية ومبلغ المصروف

### توقع الإنفاق الشهري
- **الإنفاق الحالي**: ما تم إنفاقه حتى الآن
- **التوقع للشهر**: توقع إجمالي الإنفاق
- **دقة التوقع**: مستوى الثقة في التوقع
- **شريط التقدم**: تقدم الشهر الحالي

### تحليل التصنيفات
1. اختر التصنيف المراد تحليله
2. حدد فترة التحليل (7-365 يوم)
3. اضغط "تحليل التصنيف"
4. ستحصل على:
   - إجمالي الإنفاق في التصنيف
   - متوسط المصروف
   - تكرار الإنفاق
   - نصائح مخصصة

### كشف الإنفاق غير العادي
- اضغط "فحص الإنفاق غير العادي"
- سيعرض المصاريف التي تتجاوز المعدل الطبيعي
- تصنيف الخطورة (عالية/متوسطة)
- مقدار الانحراف عن المتوسط

## 📊 التقارير

### تصدير التقرير الشهري
1. اضغط زر "التقارير" في الشريط الجانبي
2. سيتم إنشاء تقرير PDF للشهر الحالي
3. يحتوي على:
   - ملخص المصاريف
   - توزيع التصنيفات
   - مقارنة مع الشهر السابق
   - رسوم بيانية

## ⚙️ نصائح للاستخدام الأمثل

### إدخال البيانات
- **كن دقيقاً**: أدخل أسماء واضحة للمصاريف
- **استخدم التصنيف التلقائي**: يوفر الوقت ويحسن الدقة
- **أضف ملاحظات**: للمصاريف المهمة أو غير الواضحة

### إدارة الميزانيات
- **ابدأ بميزانيات واقعية**: لا تضع حدود صعبة التحقيق
- **راجع شهرياً**: عدّل الميزانيات بناءً على الأداء
- **استخدم التنبيهات**: لتجنب تجاوز الحدود

### تحقيق الأهداف
- **أهداف محددة**: حدد أهدافاً واضحة وقابلة للقياس
- **أهداف زمنية**: ضع تواريخ مستهدفة واقعية
- **إضافة تدريجية**: أضف مبالغ صغيرة بانتظام

### الاستفادة من المساعد الذكي
- **راجع النصائح**: اقرأ النصائح الشخصية بانتظام
- **حلل الأنماط**: استخدم تحليل التصنيفات لفهم عاداتك
- **تابع التوقعات**: راقب توقعات الإنفاق الشهري

## 🔧 حل المشاكل الشائعة

### التطبيق لا يبدأ
- تأكد من تثبيت Python و PyQt5
- تحقق من وجود جميع الملفات المطلوبة

### البيانات لا تظهر
- تأكد من إضافة مصاريف أولاً
- جرب تحديث الصفحة (زر التحديث)

### الرسوم البيانية لا تعمل
- تأكد من تثبيت matplotlib
- أعد تشغيل التطبيق

### مشاكل في التصنيف التلقائي
- تأكد من كتابة اسم واضح للمصروف
- جرب كلمات مختلفة أو أكثر تفصيلاً

## 📞 الدعم والمساعدة

للحصول على مساعدة إضافية أو الإبلاغ عن مشاكل:
- راجع ملف ACHIEVEMENTS_SUMMARY.md للميزات المتاحة
- تحقق من ملفات التطوير للتفاصيل التقنية
- استخدم ميزة "تنبيه تجريبي" لاختبار النظام

---

🎉 **مبروك! أنت الآن جاهز لاستخدام برنامج إدارة المصاريف الشخصية بكفاءة عالية!** 🎉
