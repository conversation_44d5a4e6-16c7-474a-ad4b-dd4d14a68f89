# 🚀 دليل البدء السريع - برنامج إدارة المصاريف الشخصية

## 📥 التثبيت السريع

### الطريقة الأولى: تشغيل ملف التثبيت
```bash
# على Windows
install.bat

# على macOS/Linux
pip install PyQt5 matplotlib reportlab arabic-reshaper python-bidi
```

### الطريقة الثانية: التثبيت اليدوي
```bash
pip install -r requirements.txt
```

## ▶️ تشغيل التطبيق

```bash
# الطريقة الأولى (مع شاشة ترحيبية)
python app.py

# الطريقة الثانية (تشغيل مباشر)
python run.py
```

## 🎯 الاستخدام السريع

### 1. إضافة مصروف جديد
- اضغط على "إضافة مصروف" في الشريط الجانبي
- املأ البيانات المطلوبة
- اضغط "حفظ المصروف"

### 2. عرض الإحصائيات
- اذهب إلى "لوحة المعلومات"
- اختر الفترة الزمنية من القائمة المنسدلة
- شاهد الرسوم البيانية والإحصائيات

### 3. إدارة المصاريف
- اذهب إلى "إدارة المصاريف"
- انقر نقرة مزدوجة على أي مصروف للتعديل
- اضغط Delete لحذف مصروف محدد

### 4. تصدير التقارير
- اضغط على "التقارير" في الشريط الجانبي
- اختر الشهر والسنة
- سيتم إنشاء ملف PDF تلقائياً

## 🔧 حل المشاكل الشائعة

### مشكلة: "ModuleNotFoundError"
**الحل:** تأكد من تثبيت جميع المكتبات:
```bash
pip install PyQt5 matplotlib reportlab arabic-reshaper python-bidi
```

### مشكلة: النصوص العربية لا تظهر بشكل صحيح
**الحل:** تأكد من تثبيت:
```bash
pip install arabic-reshaper python-bidi
```

### مشكلة: الرسوم البيانية لا تظهر
**الحل:** تأكد من تثبيت matplotlib:
```bash
pip install matplotlib
```

## 📁 ملفات المشروع

- `app.py` - الملف الرئيسي مع شاشة ترحيبية
- `run.py` - تشغيل مباشر بدون شاشة ترحيبية
- `main_window.py` - النافذة الرئيسية
- `database.py` - إدارة قاعدة البيانات
- `styles.qss` - ملف الأنماط
- `expenses.db` - قاعدة البيانات (تُنشأ تلقائياً)

## 🎨 تخصيص التطبيق

لتغيير الألوان، عدل ملف `styles.qss`:
```css
/* تغيير لون الشريط الجانبي */
#sidebar {
    background-color: #your-color;
}
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python 3.7+
2. تأكد من تثبيت جميع المكتبات المطلوبة
3. تأكد من وجود صلاحيات الكتابة في مجلد التطبيق

---
**استمتع باستخدام برنامج إدارة المصاريف الشخصية! 💰**
